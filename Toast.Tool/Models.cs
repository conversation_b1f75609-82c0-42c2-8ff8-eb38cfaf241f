using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Runtime.Serialization;
using System.Text;
using System.Text.Json.Serialization;

namespace Toast.Tool
{
    internal class AuthorizationBody
    {
        [JsonProperty("clientId")]
        public string ClientId { get; set; }

        [JsonProperty("clientSecret")]
        public string ClientSecret { get; set; }

        [JsonProperty("userAccessType")]
        public string UserAccessType { get; set; }
    }

    public partial class AuthResponse
    {
        [JsonProperty("@class")]
        public string Class { get; set; }

        [JsonProperty("token")]
        public AuthToken AuthToken { get; set; }

        [JsonProperty("status")]
        public string Status { get; set; }
    }

    public partial class AuthToken
    {
        [JsonProperty("tokenType")]
        public string TokenType { get; set; }

        [JsonProperty("scope")]
        public string Scope { get; set; }

        [JsonProperty("expiresIn")]
        public long ExpiresIn { get; set; }

        [JsonProperty("accessToken")]
        public string AccessToken { get; set; }

        [JsonProperty("idToken")]
        public object IdToken { get; set; }

        [JsonProperty("refreshToken")]
        public object RefreshToken { get; set; }
    }

    public partial class Deposit
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }
        [JsonProperty("date")] public string Date { get; set; }
        [JsonProperty("amount")] public decimal? Amount { get; set; }
        [JsonProperty("undoes")] public string Undoes { get; set; }
        [JsonProperty("employee")] public Reference EmployeeReference { get; set; }
    }

    public partial class Job
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("entityType")] public string EntityType { get; set; }

        [JsonProperty("externalId")] public string ExternalId { get; set; }

        [JsonProperty("createdDate")] public string CreatedDate { get; set; }

        [JsonProperty("deleted")] public bool Deleted { get; set; }

        [JsonProperty("code")] public string Code { get; set; }

        [JsonProperty("deletedDate")] public string DeletedDate { get; set; }

        [JsonProperty("modifiedDate")] public string ModifiedDate { get; set; }

        [JsonProperty("tipped")] public bool Tipped { get; set; }

        [JsonProperty("title")] public string Title { get; set; }

        [JsonProperty("defaultWage")] public Decimal? DefaultWage { get; set; }

        [JsonProperty("wageFrequency")] public string WageFrequency { get; set; }
    }

    public class TimeEntryBreak
    {
        [JsonProperty("breakType")] public Reference BreakType { get; set; }

        [JsonProperty("inDate")] public string InDate { get; set; }

        [JsonProperty("outDate")] public string OutDate { get; set; }

        [JsonProperty("missed")] public bool Missed { get; set; }

        [JsonProperty("paid")] public bool Paid { get; set; }
    }

    public partial class TimeEntry
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("entityType")] public object EntityType { get; set; }

        [JsonProperty("externalId")] public string ExternalId { get; set; }

        [JsonProperty("nonCashSales")] public decimal NonCashSales { get; set; }

        [JsonProperty("outDate")] public string OutDate { get; set; }

        [JsonProperty("overtimeHours")] public decimal OvertimeHours { get; set; }

        [JsonProperty("breaks")] public List<TimeEntryBreak> Breaks { get; set; }

        [JsonProperty("employeeReference")] public Reference EmployeeReference { get; set; }

        [JsonProperty("shiftReference")] public object ShiftReference { get; set; }

        [JsonProperty("nonCashGratuityServiceCharges")]
        public decimal? NonCashGratuityServiceCharges { get; set; }

        [JsonProperty("inDate")] public string InDate { get; set; }

        [JsonProperty("regularHours")] public decimal RegularHours { get; set; }

        [JsonProperty("jobReference")] public Reference JobReference { get; set; }

        [JsonProperty("tipsWithheld")] public decimal TipsWithheld { get; set; }

        [JsonProperty("businessDate")] public string BusinessDate { get; set; }

        [JsonProperty("cashGratuityServiceCharges")]
        public decimal? CashGratuityServiceCharges { get; set; }

        [JsonProperty("createdDate")] public string CreatedDate { get; set; }

        [JsonProperty("deleted")] public bool Deleted { get; set; }

        [JsonProperty("deletedDate")] public object DeletedDate { get; set; }

        [JsonProperty("cashSales")] public decimal CashSales { get; set; }

        [JsonProperty("hourlyWage")] public decimal? HourlyWage { get; set; }

        [JsonProperty("nonCashTips")] public decimal? NonCashTips { get; set; }

        [JsonProperty("modifiedDate")] public string ModifiedDate { get; set; }

        [JsonProperty("declaredCashTips")] public decimal? DeclaredCashTips { get; set; }
    }

    public partial class Reference
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("entityType")] public string EntityType { get; set; }

        [JsonProperty("externalId")] public object ExternalId { get; set; }
    }

    public partial class RestaurantInfo
    {
        [JsonProperty("guid")] public string Id { get; set; }

        [JsonProperty("code")] public string Code { get; set; }
    }

    public partial class Restaurant
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("general")] public General General { get; set; }

        [JsonProperty("urls")] public Urls Urls { get; set; }

        [JsonProperty("location")] public Location Location { get; set; }

        [JsonProperty("schedules")] public Schedules Schedules { get; set; }

        [JsonProperty("delivery")] public RestaurantDelivery Delivery { get; set; }

        [JsonProperty("onlineOrdering")] public OnlineOrdering OnlineOrdering { get; set; }

        [JsonProperty("prepTimes")] public PrepTimes PrepTimes { get; set; }
    }

    public partial class RestaurantDelivery
    {
        [JsonProperty("enabled")] public bool Enabled { get; set; }

        [JsonProperty("minimum")] public object Minimum { get; set; }

        [JsonProperty("area")] public string Area { get; set; }
    }

    public partial class General
    {
        [JsonProperty("name")] public string Name { get; set; }

        [JsonProperty("locationName")] public string LocationName { get; set; }

        [JsonProperty("locationCode")] public long LocationCode { get; set; }

        [JsonProperty("description")] public string Description { get; set; }

        [JsonProperty("timeZone")] public string TimeZone { get; set; }

        [JsonProperty("closeoutHour")] public long CloseoutHour { get; set; }

        [JsonProperty("managementGroupGuid")] public Guid ManagementGroupGuid { get; set; }
    }

    public partial class Location
    {
        [JsonProperty("address1")] public string Address1 { get; set; }

        [JsonProperty("address2")] public string Address2 { get; set; }

        [JsonProperty("city")] public string City { get; set; }

        [JsonProperty("stateCode")] public string StateCode { get; set; }

        [JsonProperty("zipCode")] public long ZipCode { get; set; }

        [JsonProperty("country")] public string Country { get; set; }

        [JsonProperty("phone")] public string Phone { get; set; }

        [JsonProperty("latitude")] public double Latitude { get; set; }

        [JsonProperty("longitude")] public double Longitude { get; set; }
    }

    public partial class OnlineOrdering
    {
        [JsonProperty("enabled")] public bool Enabled { get; set; }

        [JsonProperty("scheduling")] public bool Scheduling { get; set; }

        [JsonProperty("specialRequests")] public bool SpecialRequests { get; set; }

        [JsonProperty("specialRequestsMessage")]
        public string SpecialRequestsMessage { get; set; }

        [JsonProperty("paymentOptions")] public PaymentOptions PaymentOptions { get; set; }
    }

    public partial class PaymentOptions
    {
        [JsonProperty("delivery")] public TakeoutClass Delivery { get; set; }

        [JsonProperty("takeout")] public TakeoutClass Takeout { get; set; }

        [JsonProperty("ccTip")] public bool CcTip { get; set; }
    }

    public partial class TakeoutClass
    {
        [JsonProperty("cash")] public bool Cash { get; set; }

        [JsonProperty("ccSameDay")] public bool CcSameDay { get; set; }

        [JsonProperty("ccFuture")] public bool CcFuture { get; set; }

        [JsonProperty("ccInStore", NullValueHandling = NullValueHandling.Ignore)]
        public bool? CcInStore { get; set; }
    }

    public partial class PrepTimes
    {
        [JsonProperty("deliveryPrepTime")] public long DeliveryPrepTime { get; set; }

        [JsonProperty("deliveryTimeAfterOpen")]
        public long DeliveryTimeAfterOpen { get; set; }

        [JsonProperty("deliveryTimeBeforeClose")]
        public long DeliveryTimeBeforeClose { get; set; }

        [JsonProperty("takeoutPrepTime")] public long TakeoutPrepTime { get; set; }

        [JsonProperty("takeoutTimeAfterOpen")] public long TakeoutTimeAfterOpen { get; set; }

        [JsonProperty("takeoutTimeBeforeClose")]
        public long TakeoutTimeBeforeClose { get; set; }

        [JsonProperty("takeoutThrottlingTime")]
        public long TakeoutThrottlingTime { get; set; }

        [JsonProperty("deliveryThrottlingTime")]
        public long DeliveryThrottlingTime { get; set; }
    }

    public partial class Schedules
    {
        [JsonProperty("daySchedules")] public Dictionary<string, DaySchedule> DaySchedules { get; set; }

        [JsonProperty("weekSchedule")] public WeekSchedule WeekSchedule { get; set; }
    }

    public partial class DaySchedule
    {
        [JsonProperty("scheduleName")] public string ScheduleName { get; set; }

        [JsonProperty("services")] public List<Service> Services { get; set; }

        [JsonProperty("openTime")] public DateTimeOffset OpenTime { get; set; }

        [JsonProperty("closeTime")] public DateTimeOffset CloseTime { get; set; }
    }

    public partial class Service
    {
        [JsonProperty("name")] public string Name { get; set; }

        [JsonProperty("hours")] public Hours Hours { get; set; }

        [JsonProperty("overnight")] public bool Overnight { get; set; }
    }

    public partial class Hours
    {
        [JsonProperty("startTime")] public DateTimeOffset StartTime { get; set; }

        [JsonProperty("endTime")] public DateTimeOffset EndTime { get; set; }
    }

    public partial class WeekSchedule
    {
        [JsonProperty("monday")] public string Monday { get; set; }

        [JsonProperty("tuesday")] public string Tuesday { get; set; }

        [JsonProperty("wednesday")] public string Wednesday { get; set; }

        [JsonProperty("thursday")] public string Thursday { get; set; }

        [JsonProperty("friday")] public string Friday { get; set; }

        [JsonProperty("saturday")] public string Saturday { get; set; }

        [JsonProperty("sunday")] public string Sunday { get; set; }
    }

    public partial class Urls
    {
        [JsonProperty("website")] public string Website { get; set; }

        [JsonProperty("facebook")] public string Facebook { get; set; }

        [JsonProperty("twitter")] public Uri Twitter { get; set; }

        [JsonProperty("orderOnline")] public Uri OrderOnline { get; set; }

        [JsonProperty("purchaseGiftCard")] public Uri PurchaseGiftCard { get; set; }

        [JsonProperty("checkGiftCard")] public Uri CheckGiftCard { get; set; }
    }

    public class EmployeeUpdate
    {
        [JsonProperty("firstName")] public string FirstName { get; set; }

        [JsonProperty("lastName")] public string LastName { get; set; }
    }

    // keep separate to avoid accidentally changing
    public class PasscodeUpdate
    {
        [JsonProperty("passcode")] public string Passcode { get; set; }
    }

    public class WageOverride
    {
        [JsonProperty("wage")] public double Wage { get; set; }

        [JsonProperty("jobReference")] public Reference JobReference { get; set; }
    }

    public class Client
    {
        [JsonProperty("restaurantGuid")]
        public Guid RestaurantGuid { get; set; }

        [JsonProperty("managementGroupGuid")]
        public Guid ManagementGroupGuid { get; set; }

        [JsonProperty("createdByEmailAddress", NullValueHandling = NullValueHandling.Ignore)]
        public string CreatedByEmailAddress { get; set; }

        [JsonProperty("restaurantName", NullValueHandling = NullValueHandling.Ignore)]
        public string RestaurantName { get; set; }
    }

    public partial class Employee
    {
        public Employee()
        {
            JobReferences = new List<Reference>();
            WageOverrides = new List<WageOverride>();
        }

        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("entityType", NullValueHandling = NullValueHandling.Ignore)]
        public string EntityType { get; set; }

        [JsonProperty("externalId", NullValueHandling = NullValueHandling.Ignore)]
        public string ExternalId { get; set; }

        [JsonProperty("lastName")] public string LastName { get; set; }

        [JsonProperty("wageOverrides", NullValueHandling = NullValueHandling.Ignore)]
        public List<WageOverride> WageOverrides { get; set; }

        [JsonProperty("firstName")] public string FirstName { get; set; }

        [JsonProperty("createdDate", NullValueHandling = NullValueHandling.Ignore)]
        public string CreatedDate { get; set; }

        [JsonProperty("deleted")] public bool Deleted { get; set; }

        [JsonProperty("deletedDate", NullValueHandling = NullValueHandling.Ignore)]
        public string DeletedDate { get; set; }

        [JsonProperty("jobReferences", NullValueHandling = NullValueHandling.Ignore)]
        public List<Reference> JobReferences { get; set; }

        [JsonProperty("modifiedDate", NullValueHandling = NullValueHandling.Ignore)]
        public string ModifiedDate { get; set; }

        [JsonProperty("externalEmployeeId")] public string ExternalEmployeeId { get; set; }

        [JsonProperty("email")] public string Email { get; set; }

        [JsonProperty("phoneNumber")] public string PhoneNumber { get; set; }

        [JsonProperty("passcode")] public string Passcode { get; set; }

        public bool ShouldSerializeGuid()
        {
            return false;
        }

        public bool ShouldSerializeDeleted()
        {
            return false;
        }
        public bool ShouldSerializeCreatedDate()
        {
            return false;
        }

        public bool ShouldSerializeModifiedDate()
        {
            return false;
        }
    }

    [Newtonsoft.Json.JsonConverter(typeof(Newtonsoft.Json.Converters.StringEnumConverter))]
    [System.Text.Json.Serialization.JsonConverter(typeof(System.Text.Json.Serialization.JsonStringEnumConverter))]
    public enum CashEntryType
    {
        [EnumMember(Value = "CASH_IN")]
        [JsonPropertyName("CASH_IN")]
        CashIn,

        [EnumMember(Value = "CASH_COLLECTED")]
        [JsonPropertyName("CASH_COLLECTED")]
        CashCollected,

        [EnumMember(Value = "CASH_OUT")]
        [JsonPropertyName("CASH_OUT")]
        CashOut,

        [EnumMember(Value = "PAY_OUT")]
        [JsonPropertyName("PAY_OUT")]
        PayOut,

        [EnumMember(Value = "UNDO_PAY_OUT")]
        [JsonPropertyName("UNDO_PAY_OUT")]
        UndoPayOut,

        [EnumMember(Value = "TIP_OUT")]
        [JsonPropertyName("TIP_OUT")]
        TipOut,

        [EnumMember(Value = "NO_SALE")]
        [JsonPropertyName("NO_SALE")]
        NoSale,

        [EnumMember(Value = "DRIVER_REIMBURSEMENT")]
        [JsonPropertyName("DRIVER_REIMBURSEMENT")]
        DriverReimbursement,

        [EnumMember(Value = "CLOSE_OUT_EXACT")]
        [JsonPropertyName("CLOSE_OUT_EXACT")]
        CloseOutExact,

        [EnumMember(Value = "CLOSE_OUT_OVERAGE")]
        [JsonPropertyName("CLOSE_OUT_OVERAGE")]
        CloseOutOverage,

        [EnumMember(Value = "CLOSE_OUT_SHORTAGE")]
        [JsonPropertyName("CLOSE_OUT_SHORTAGE")]
        CloseOutShortage
    }

    public partial class CashEntry
    {
        [JsonProperty("guid")] public Guid Guid { get; set; }

        [JsonProperty("entityType")] public string EntityType { get; set; }

        [JsonProperty("amount")] public decimal Amount { get; set; }

        [JsonProperty("reason")] public string Reason { get; set; }

        [JsonProperty("date")] public string Date { get; set; }

        [JsonProperty("type")] public CashEntryType Type { get; set; }

        [JsonProperty("cashDrawer")] public Reference CashDrawer { get; set; }

        [JsonProperty("payoutReason")] public Reference PayoutReason { get; set; }

        [JsonProperty("noSaleReason")] public Reference NoSaleReason { get; set; }

        [JsonProperty("undoes")] public string Undoes { get; set; }

        [JsonProperty("employee1")] public Reference Employee1 { get; set; }

        [JsonProperty("employee2")] public Reference Employee2 { get; set; }

        [JsonProperty("creatorOrShiftReviewSubject")] public Reference CreatorOrShiftReviewSubject { get; set; }

        [JsonProperty("approverOrShiftReviewSubject")] public Reference ApproverOrShiftReviewSubject { get; set; }
    }
}
