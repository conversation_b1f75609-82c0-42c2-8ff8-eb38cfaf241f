using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using CommandLine;

namespace Toast.Tool
{
    public class Program : ProgramBase<SettingCommand>
    {
        static readonly string AppVersion = "1.6.0";

        public override int ShowUsage()
        {
            Console.WriteLine("Usage: Toast.Tool.exe <command> <command-args>");
            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cash [list] = cash management");
            Console.WriteLine("   - employee [add, list, active, all, terms, broken, valid, exceptions, connections, view, archive, unarchive] = employee management");
            Console.WriteLine("       * active - print active employees");
            Console.WriteLine("       * add - add an employee");
            Console.WriteLine("       * all - print all employees");
            Console.WriteLine("       * broken - print employees without external employee id");
            Console.WriteLine("       * list - print all active employees");
            Console.WriteLine("       * terms - print terminated employees");
            Console.WriteLine("       * valid - print all employees with non-blank external employee id");
            Console.WriteLine("       * exceptions - print all employees with a blank external employee id");
            Console.WriteLine("       * connections - print employee connections");
            Console.WriteLine("       * view - view a specific employee");
            Console.WriteLine("       * archive - archive an employee");
            Console.WriteLine("       * unarchive - unarchive an employee");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - job [list] = job management");
            Console.WriteLine("   - export [alltime, time] = export utilities");
            Console.WriteLine("   - import [changes, employees] = import utilities");
            Console.WriteLine("   - restaurant [list, view, employees] = restaurant management");
            Console.WriteLine("   - setting = settings management");
            Console.WriteLine("   - version - show version");

            return 0;
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Version(List<string> args)
        {
            Console.WriteLine($"{AppVersion} ({BuildInfo.Branch}-{BuildInfo.Commit}:{BuildInfo.Built}-{BuildInfo.Host})");
        }

        public void Info(List<string> args)
        {
            Payroll.Shared.Setting.Init();
            Console.WriteLine($"Toast.Tool.exe Config: {Payroll.Shared.Setting.IniFilePath})");
            Version(args);
            Console.WriteLine();

            Console.WriteLine("Settings");
            Console.WriteLine("===================================");
            var list = Payroll.Shared.Setting.ListSection("toast");
            foreach (var e in list)
            {
                Console.WriteLine($"{e.Key} = {e.Value}");
            }

            Console.WriteLine();
            Console.WriteLine("Manager Jobs");
            Console.WriteLine("===================================");
            var managerJobs = Config.ManagerJobsById();
            foreach (var mjob in managerJobs)
            {
                Console.WriteLine(mjob);
            }

            Console.WriteLine();
            Console.WriteLine("Fields");
            Console.WriteLine("===================================");
            Console.WriteLine($"Email: using '{FieldService.EmailFieldName}' email address");
            if (string.IsNullOrEmpty(FieldService.PasscodeFieldName))
                Console.WriteLine("Passcode: *updates are disabled*");
            else
                Console.WriteLine($"Passcode: using the '{FieldService.PasscodeFieldName}' field");

            /*
            var codeMap = Config.RestaurantsByCode();
            Console.WriteLine();
            Console.WriteLine("Restaurants");
            foreach (var item in codeMap)
            {
                Console.WriteLine(".{0}={1}.", item.Key, item.Value.Id);
            }
            */

            Console.WriteLine();
            Console.WriteLine("To set an api call delay of N seconds, use...");
            Console.WriteLine("  Toast.Tool.exe setting delay <N>");

            Console.WriteLine();
            Console.WriteLine("To run in execution mode...");
            Console.WriteLine("  Toast.Tool.exe setting mode execute");
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public void Cash(List<string> args)
        {
            ExecCommand<CashCommand>(args);
        }

        public void Client(List<string> args)
        {
            ExecCommand<ClientCommand>(args);
        }

        public void Employee(List<string> args)
        {
            ExecCommand<EmployeeCommand>(args);
        }

        public void Job(List<string> args)
        {
            ExecCommand<JobCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Settings(List<string> args)
        {
            ExecCommand<SettingCommand>(args);
        }

        public void Restaurant(List<string> args)
        {
            ExecCommand<RestaurantCommand>(args);
        }

        static int Main(string[] args)
        {
            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"Toast.Tool.Exe - Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"Toast.Tool.exe Config: {Payroll.Shared.Setting.IniFilePath}");
            Log.Logger.Information($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
