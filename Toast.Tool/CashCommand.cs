using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace Toast.Tool
{
    class CashCommand
    {
        public void Entries(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Toast.Tool.exe cash entries <code> <day>");
                Console.WriteLine(
                    "  where: <day> is an integer representing how many days prior to today");
                return;
            }

            var codeMap = Config.RestaurantsByCode();
            if (!codeMap.TryGetValue(args[0], out var rInfo))
            {
                Console.WriteLine($"Invalid restaurant code: {args[0]}");
                return;
            }

            var daysBack = Convert.ToInt32(args[1]);
            var date = DateTime.UtcNow.Date.AddDays(-daysBack);

            try
            {
                using (var service = new ToastService())
                {
                    var entries = service.GetCashEntries(rInfo.Id, date).Result;

                    if (entries.Count == 0)
                    {
                        Console.WriteLine("No cash entries found for the specified date.");
                        return;
                    }

                    Console.WriteLine($"Cash entries for {rInfo.Code} on {date:yyyy-MM-dd}:");
                    Console.WriteLine("Date\t\tAmount\t\tReason\t\tType");
                    Console.WriteLine(new string('-', 80));

                    foreach (var entry in entries)
                    {
                        Console.WriteLine($"{entry.Date}\t\t{entry.Amount}\t\t{entry.Reason ?? "N/A"}\t\t{entry.Type}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Deposits(List<string> args)
        {
            if (args == null || args.Count < 2)
            {
                Console.WriteLine("Usage: Toast.Tool.exe cash deposits <code> <day>");
                Console.WriteLine(
                    "  where: <day> is an integer representing how many days prior to today");
                return;
            }

            var codeMap = Config.RestaurantsByCode();
            if (!codeMap.TryGetValue(args[0], out var rInfo))
            {
                Console.WriteLine($"Invalid restaurant code: {args[0]}");
                return;
            }

            var daysBack = Convert.ToInt32(args[1]);
            var date = DateTime.UtcNow.Date.AddDays(-daysBack);

            try
            {
                using (var service = new ToastService())
                {
                    var deposits = service.Deposits(rInfo.Id, date).Result;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        public void Audit(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Toast.Tool.exe cash audit <day>");
                Console.WriteLine("  where:");
                Console.WriteLine("      <day> is an integer representing how many days prior to today");

                return;
            }

            var daysBack = Convert.ToInt32(args[0]);
            var date = DateTime.UtcNow.Date.AddDays(-daysBack);
            var restaurants = Config.RestaurantsByCode();

            var results = new List<Result>();
            var cleanResult = new Result();

            foreach (var restaurant in restaurants)
            {
                try
                {
                    using (var service = new ToastService())
                    {
                        var entries = service.GetCashEntries(restaurant.Value.Id, date).Result;
                        var totalAmount = entries.Sum(e => e.Amount);

                        if (totalAmount == 0)
                        {
                            var contact = Config.GetRestaurantContactEmail(restaurant.Value.Id);

                            var result = new Result()
                            {
                                ResultType = ResultType.Error,
                                Success = false,
                                NotifyAddress = contact,
                                Args = new SortedDictionary<string, string>
                                {
                                    { "Restaurant", restaurant.Key },
                                    { "AuditDate", date.ToString("yyyy-MM-dd") },
                                    { "CashAudit", "failed" },
                                    { "Deposit", totalAmount.ToString("F2") }
                                }
                            };

                            results.Add(result);
                        }
                        else
                        {
                            cleanResult.AddArg(restaurant.Key, totalAmount.ToString("F2"));
                        }

                        // this seems to be a particularly sensitive endpoint rate request wise
                        // so we need to wait a bit between requests
                        Thread.Sleep(1000);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"{restaurant.Key}: Error - {e.Message}");
                    Log.Logger.Error($"Error getting cash entries for {restaurant.Key}: {e.Message}");
                }
            }

            results.Add(cleanResult);
            ConsoleService.PrintFormattedJson(results);
        }

        public void Review(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Toast.Tool.exe cash review <day>");
                Console.WriteLine("  where:");
                Console.WriteLine("      <day> is an integer representing how many days prior to today");

                return;
            }

            var daysBack = Convert.ToInt32(args[0]);
            var date = DateTime.UtcNow.Date.AddDays(-daysBack);
            var restaurants = Config.RestaurantsByCode();

            Console.WriteLine($"Cash entries audit for {date:yyyy-MM-dd}:");

            foreach (var restaurant in restaurants)
            {
                try
                {
                    using (var service = new ToastService())
                    {
                        var entries = service.GetCashEntries(restaurant.Value.Id, date).Result;
                        var totalAmount = entries.Sum(e => e.Amount);
                        Console.WriteLine($"{restaurant.Key}: ${totalAmount:F2}");

                        // this seems to be a particularly sensitive endpoint rate request wise
                        // so we need to wait a bit between requests
                        Thread.Sleep(1000);
                    }
                }
                catch (Exception e)
                {
                    Console.WriteLine($"{restaurant.Key}: Error - {e.Message}");
                    Log.Logger.Error($"Error getting cash entries for {restaurant.Key}: {e.Message}");
                }
            }
        }
    }
}
