﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Payroll.Shared
{
    public class EmployeeDirectoryEntry
    {
        public string GivenName { get; set; }
        public string SurName { get; set; }
        public string MiddleName { get; set; }
        public string Name { get; set; }
        public string SamAccountName { get; set; }
        public string ParentOU { get; set; }
        public string PolicyDN { get; set; }
        public string PrimaryKey { get; set; }
        public string City { get; set; }
        public string Comment { get; set; }
        public string Company { get; set; }
        public string Department { get; set; }
        public string Description { get; set; }
        public string DisplayName { get; set; }
        public string DivisionName { get; set; }
        public string DivisionCode { get; set; }
        public string Email { get; set; }
        public string BadgeNo { get; set;  }
        public string EmployeeNumber { get; set; }
        public string EmployeeId { get; set; }
        public string ExtensionAttribute1 { get; set; }
        public string ExtensionAttribute2 { get; set; }
        public string ExtensionAttribute3 { get; set; }
        public string ExtensionAttribute4 { get; set; }
        public string ExtensionAttribute5 { get; set; }
        public string ExtensionAttribute6 { get; set; }
        public string ExtensionAttribute7 { get; set; }
        public string ExtensionAttribute8 { get; set; }
        public string ExtensionAttribute9 { get; set; }
        public string ExtensionAttribute10 { get; set; }
        public string ExtensionAttribute11 { get; set; }

        public string Mail { get; set; }
        public string Manager { get; set; }
        public string MobilePhone { get; set; }
        public string OfficePhone { get; set; }
        public string PostalCode { get; set; }
        public string State { get; set; }
        public string StreetAddress { get; set; }
        public string Title { get; set; }
        public string PersonalTitle { get; set; }
        public string ExternalId { get; set; }
        public List<string> MemberOf { get; set; } = new List<string>();

        public DateTime? HireDate { get; set; }
        public DateTime? TermDate { get; set; }
        public DateTime? LastUpdated { get; set; }

        public bool Active { get; set; }
    }
}
