using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace Payroll.Shared
{
    public enum JobLevel
    {
        None = 0, Cashier = 1, Manager = 2
    };

    public class Job
    {
        public Job()
        {
            JobLevel = JobLevel.None;
        }

        [Newtonsoft.Json.JsonProperty("Code")]
        [System.Text.Json.Serialization.JsonPropertyName("Code")]
        public string Code { get; set; }

        [Newtonsoft.Json.JsonProperty(NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
        [System.Text.Json.Serialization.JsonPropertyName("Name")]
        public string Name { get; set; }

        [Newtonsoft.Json.JsonProperty("Rate")]
        [Newtonsoft.Json.JsonConverter(typeof(DecimalFormatConverter))]
        [System.Text.Json.Serialization.JsonConverter(typeof(DecimalFormatJsonConverter))]
        [System.Text.Json.Serialization.JsonPropertyName("Rate")]
        public decimal Rate { get; set; }

        [Newtonsoft.Json.JsonProperty("IsPrimary")]
        [System.Text.Json.Serialization.JsonPropertyName("IsPrimary")]
        public bool IsPrimary { get; set; }

        [Newtonsoft.Json.JsonProperty(NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
        [System.Text.Json.Serialization.JsonPropertyName("Location")]
        public string Location { get; set; }

        [Newtonsoft.Json.JsonProperty("JobLevel")]
        [Newtonsoft.Json.JsonConverter(typeof(JobLevelNewtonsoftConverter))]
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingDefault)]
        [System.Text.Json.Serialization.JsonPropertyName("JobLevel")]
        public JobLevel JobLevel { get; set; }

        [Newtonsoft.Json.JsonProperty(NullValueHandling = Newtonsoft.Json.NullValueHandling.Ignore)]
        [System.Text.Json.Serialization.JsonIgnore(Condition = System.Text.Json.Serialization.JsonIgnoreCondition.WhenWritingNull)]
        [System.Text.Json.Serialization.JsonPropertyName("EffectiveDate")]
        public DateTime? EffectiveDate { get; set; }

        public bool ShouldSerializeJobLevel()
        {
            return JobLevel != JobLevel.None;
        }
    }
}
