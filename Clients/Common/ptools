#!/bin/bash
#
# To mark an image as the current production image, run a command like the following: 
#   podman tag ghcr.io/oa-labs/upwards-ptools:1.1.0-upwards.1-effcf9fa upward-tools:production
#
ROOTDIR=/opt/openarc
podman run --rm --env-host -v $ROOTDIR/Logs:$ROOTDIR/Logs -v $ROOTDIR/Mailbox:$ROOTDIR/Mailbox -v /usr/share/PayrollTools:/usr/share/PayrollTools ghcr.io/oa-labs/upwards-ptools:production $@
