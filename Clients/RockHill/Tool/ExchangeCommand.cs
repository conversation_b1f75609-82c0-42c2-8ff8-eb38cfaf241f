﻿using Payroll.Shared;
using Serilog;
using System.Text.Json;
using System.Management.Automation;
using System.Collections;
using System.Management.Automation.Runspaces;
using Microsoft.PowerShell.Commands;

namespace RockHill.Tool
{
    public class ExchangeCommand
    {
        private static bool ValidForExchangeAccount(Employee employee)
        {
            // we use to have a more extensive set of checks, now every valid employee gets an email address
            return Converter.ValidNew<PERSON>ire(employee);
        }

        private static List<EmployeeDirectoryEntry> ValidNewHiresForExchangeToDirectoryEntries(IEnumerable<Employee> employees)
        {
            return Converter.EmployeesToDirectoryEntries(employees, ValidForExchangeAccount);
        }

        public int Test(List<string> args)
        {
            try
            {
                using (Runspace runspace = RunspaceFactory.CreateRunspace())
                {
                    runspace.Open();

                    using (var powerShell = PowerShell.Create())
                    {
                        powerShell.Runspace = runspace;

                        powerShell.AddCommand("Get-ChildItem");
                        var results = powerShell.Invoke();

                        foreach (PSObject result in results)
                        {
                            foreach (var p in result.Properties)
                            {
                                if (p.Name != "Name") continue;
                                Console.WriteLine($"prop {p.Name} = {p.Value}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        public int Refresh(List<string> args)
        {
            string scriptPath = Config.UpdateMailboxListScriptPath();
            Log.Logger.Information("Running {0}", scriptPath);
            try
            {
                using (Runspace runspace = RunspaceFactory.CreateRunspace())
                {
                    runspace.Open();

                    using (var powerShell = PowerShell.Create())
                    {
                        powerShell.Runspace = runspace;
                        powerShell.AddScript(scriptPath);
                        var results = powerShell.Invoke();

                        foreach (PSObject result in results)
                        {
                            foreach (var p in result.Properties)
                            {
                                if (p.Name != "Name") continue;
                                Console.WriteLine($"prop {p.Name} = {p.Value}");
                            }
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        public int Setup(List<string> args)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employees))
                {
                    Log.Logger.Error("Failed to parse employees list");
                    return ExitCode.BadInput;
                }

                if (employees == null || employees.Count == 0)
                {
                    Log.Logger.Error("Failed to load employees list");
                    return ExitCode.BadInput;
                }

                var dryRun = !(args != null && args.Count > 0 && args[0] == "doit");

                // if we don't process ANY employees, return an error code
                if (ProcessNewHires(employees, dryRun) == 0)
                    return ExitCode.NoData;
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                return ExitCode.Exception;
            }

            return ExitCode.Success;
        }

        // In response to Andrew's email that a new employee with 
        // same first and last name just overrides existing account with same name
        // switched from @cityofrockhillsc.gov to the .com version below
        // this is because the current-mailboxes file stores the .com versions
        private (string username, string emailAddress) CreateRockhillUserName(string fname, string lname, string mname = null)
        {
            string username = "";

            if (!string.IsNullOrEmpty(mname))
            {
                username = $"{fname}{mname.Substring(0, 1)}.{lname}";
            }
            else
            {
                username = $"{fname}.{lname}";
            }


            // align the characters here with the regex in MailboxService.IsValidEmailAddress
            var cleanUsername = username.ToLower().RemoveAllSpecialCharsExcept("_.+-");
            return (cleanUsername, $"{cleanUsername}@cityofrockhill.com");
        }

        IEnumerable<EmployeeDirectoryEntry> PruneAllExistingAccounts(IEnumerable<Payroll.Shared.EmployeeDirectoryEntry> newEmployees)
        {
            var cleanList = new List<Payroll.Shared.EmployeeDirectoryEntry>();

            foreach (var dEntry in newEmployees)
            {
                // if in emp record & email is present, skip
                if (!string.IsNullOrEmpty(dEntry.Email))
                {
                    var empId = MailboxService.ExistingMailboxEmpId(dEntry.Email);
                    Log.Logger.Debug("Employee {0}/{1} has assigned email address: {2} - lookup: {3}",
                        dEntry.SurName, dEntry.PrimaryKey, dEntry.Email, empId);

                    if (dEntry.PrimaryKey.Equals(empId, StringComparison.OrdinalIgnoreCase))
                    {
                        Log.Logger.Information("Employee {0} already has an exchange mailbox: {1}", dEntry.PrimaryKey, dEntry.Email);
                        continue;
                    }

                    // not present, good addr to use
                    if (empId == null)
                    {
                        cleanList.Add(dEntry);
                        continue;
                    }
                }

                // now try to create an email address, one of two ways. First is first+last
                {
                    var (emailAddressUserName, emailAddress) = CreateRockhillUserName(dEntry.GivenName, dEntry.SurName);
                    var empId = MailboxService.ExistingMailboxEmpId(emailAddress);
                    if (dEntry.PrimaryKey.Equals(empId, StringComparison.OrdinalIgnoreCase))
                    {
                        Log.Logger.Information("Employee {0} already has an exchange mailbox: {1}", dEntry.PrimaryKey, emailAddress);
                        continue;
                    }

                    // not present, good addr to use
                    if (empId == null)
                    {
                        dEntry.Email = emailAddress;
                        cleanList.Add(dEntry);
                        continue;
                    }
                }

                // the second is first+last+middle
                {
                    var (emailAddressUserName, emailAddress) = CreateRockhillUserName(dEntry.GivenName, dEntry.SurName, dEntry.MiddleName);
                    var empId = MailboxService.ExistingMailboxEmpId(emailAddress);
                    if (dEntry.PrimaryKey.Equals(empId, StringComparison.OrdinalIgnoreCase))
                    {
                        Log.Logger.Information("Employee {0} already has an exchange mailbox: {1}", dEntry.PrimaryKey, emailAddress);
                        continue;
                    }

                    // not present, good addr to use
                    if (empId == null)
                    {
                        dEntry.Email = emailAddress;
                        cleanList.Add(dEntry);
                        continue;
                    }
                }

                Log.Logger.Fatal("Failed to find a valid/available email address for employee {0}.", dEntry.PrimaryKey);
            }

            return cleanList;
        }

        private int ProcessNewHires(IEnumerable<Payroll.Shared.Employee> employees, bool dryRun)
        {
            // have a list of new hires
            // some have email addresses in record, some do not
            // for the ones with an email address in record, just need to see if it already is in exchange. if it is done. if not, create
            // for those without email address, need to create one
            var newEmployees = ValidNewHiresForExchangeToDirectoryEntries(employees);
            Log.Logger.Information("Processing {cnt} validated new hires", newEmployees.Count());

            var hiresWithoutExchangeAccounts = PruneAllExistingAccounts(newEmployees);
            Log.Logger.Information("Processing {cnt} validated new hires without exchange accounts...", hiresWithoutExchangeAccounts.Count());

            var ouMapping = MappingFileService.LoadParentOuMap();
            int cnt = 0;

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                var empCache = cache.GetCollection<Employee>("employees");

                foreach (var dEntry in hiresWithoutExchangeAccounts)
                {
                    // set group ou if possible
                    if (ouMapping.ContainsKey(dEntry.DivisionCode))
                        dEntry.ParentOU = ouMapping[dEntry.DivisionCode];
                    else Log.Logger.Warning("Failed to find division code: '{0}' in group OU lookup table", dEntry.DivisionCode);

                    // we just want the username here
                    int indexOfAt = dEntry.Email.IndexOf("@");
                    string username = dEntry.Email.Substring(0, indexOfAt);

                    Console.WriteLine($"{dEntry.GivenName}\t{dEntry.SurName}\t{dEntry.PrimaryKey}\t{username}\t{dEntry.ParentOU}");
                    cnt++;
                }
            }

            return cnt;
        }
    }
}