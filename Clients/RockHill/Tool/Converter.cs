﻿using Payroll.Shared;
using Serilog;
using System.Text.Json;

namespace RockHill.Tool
{
    static class Converter
    {
        static Dictionary<string, string> OuMapping { get; set; }
        static Dictionary<string, DnMapEntry> DnMapping { get; set; }

        static Converter()
        {
            OuMapping = MappingFileService.LoadParentOuMap();
            DnMapping = MappingFileService.LoadTemplateDnMap();
        }

        public static bool AddPolicyAndGroupAttributesOldNoLongerUsedDeleteNextTimeYouSee(EmployeeDirectoryEntry dEntry)
        {
            Log.Logger.Debug("Adding policy and group attributes for {0} ({1}/{2})",
                dEntry.SamAccountName, dEntry.DivisionCode, dEntry.Title);
            // set group ou if possible
            if (OuMapping.ContainsKey(dEntry.DivisionCode))
                dEntry.ParentOU = OuMapping[dEntry.DivisionCode];
            else Log.Logger.Warning("Failed to find division code: '{0}' in group OU lookup table", dEntry.DivisionCode);

            // set policy dn if possible
            var dkey = DivisionOrgUnit.CreateKey(dEntry.ExtensionAttribute3, dEntry.Title);
            if (!DnMapping.ContainsKey(dkey))
            {
                Log.Logger.Warning("Failed to find glcode/job title pair: '{0}' in DN lookup table, no predefined template available...", dkey);
                return false;
            }

            dEntry.PolicyDN = DnMapping[dkey].PolicyDN;
            return true;
        }

        public static bool AddPolicyAndGroupAttributesVersion2(EmployeeDirectoryEntry dEntry)
        {
            Log.Logger.Debug("Adding policy and group attributes for {0} ({1}/{2}) - Version 2 Mode",
                dEntry.SamAccountName, dEntry.DivisionCode, dEntry.Title);

            // set policy dn if possible
            var dkey = DivisionOrgUnit.CreateKey(dEntry.ExtensionAttribute3, dEntry.Title);
            if (!DnMapping.ContainsKey(dkey))
            {
                Log.Logger.Warning("Glcode/job title pair not found: '{0}' in DN lookup table, no predefined template available...", dkey);
                return false;
            }

            dEntry.PolicyDN = DnMapping[dkey].PolicyDN;
            dEntry.ParentOU = DnMapping[dkey].ParentOU;

            return true;
        }

        public static string FormatPhoneNumber(string value)
        {
            // this line is to protect against someone with two phone numbers in one field
            if (value.Contains("/")) return value;

            if (string.IsNullOrEmpty(value)) return string.Empty;
            var cleanValue = new System.Text.RegularExpressions.Regex(@"\D")
                .Replace(value, string.Empty);
            cleanValue = cleanValue.TrimStart('1');

            if (string.IsNullOrEmpty(cleanValue)) return value;

            if (cleanValue.Length == 7)
                return Convert.ToInt64(cleanValue).ToString("###-####");
            if (cleanValue.Length == 10)
                return Convert.ToInt64(cleanValue).ToString("###-###-####");
            if (cleanValue.Length > 10)
                return Convert.ToInt64(cleanValue)
                    .ToString("###-###-#### " + new String('#', (cleanValue.Length - 10)));

            return cleanValue;
        }

        public static string FormatNewSamAccountName(string fname, string lname)
        {
            var cleanLastName = lname.RemoveSpecialCharsExceptDash();
            var cleanFirstName = fname.RemoveSpecialCharsExceptDash();

            // some names might be two words, so we must remove spaces
            return $"{fname.ToLower()}.{lname.ToLower()}".Replace(" ", "");
        }

        public static EmployeeDirectoryEntry EmployeeToDirectoryEntry(Employee employee)
        {
            var fullName = $"{employee.LastName}, {employee.FirstName}";
            var currentTenureStart = employee.RehireDate > employee.HireDate ? employee.RehireDate : employee.HireDate;

            Dictionary<string, string> attrs = employee.Attributes;
            var divisionCode = attrs?.GetValueOrDefault("division_code") ?? "";
            var divisionName = attrs?.GetValueOrDefault("division_name") ?? "";

            var glcode = attrs?.GetValueOrDefault("gl_code") ?? "";
            var manager = attrs?.GetValueOrDefault("manager_code") ?? "";
            var managerName = attrs?.GetValueOrDefault("manager_name") ?? "";
            var businessTitle = attrs?.GetValueOrDefault("business_title") ?? "";
            var postNominals = attrs?.GetValueOrDefault("post_nominals") ?? "";
            var middleName = attrs?.GetValueOrDefault("middle_name") ?? "";
            var badgeNo = attrs?.GetValueOrDefault("badge_no") ?? "";

            // phone number processing
            var signPhone = attrs?.GetValueOrDefault("sign_phone") ?? "";
            var signPhoneFmt = FormatPhoneNumber(signPhone);
            var signMobile = attrs?.GetValueOrDefault("sign_mobile") ?? "";
            var signMobileFmt = FormatPhoneNumber(signMobile);
            var agentPhone = attrs?.GetValueOrDefault("agent_phone") ?? "";
            var cityPhone = attrs?.GetValueOrDefault("city_phone") ?? "";
            var cityPhoneFmt = FormatPhoneNumber(cityPhone);
            var cityMobile = attrs?.GetValueOrDefault("city_mobile") ?? "";

            // last name customization
            var firstNameToUse = employee.FirstName;
            var lastNameToUse = employee.LastName;

            var preferredLastName = attrs?.GetValueOrDefault(key: "pref_lname") ?? "";
            if (!string.IsNullOrEmpty(preferredLastName))
                lastNameToUse = preferredLastName;

            // we only want to set this for new employees. SSN will only be non-null for new employees
            // In the case when badge_no field does not match the ClockSeq field we want in AD the employeeNumber
            // field to always match the ClockSeq with all leading 0s removed and the last 2 of SSN added to the end
            var employeeNumberAndPrinterAccessCode = "";
            var ssn_tail = employee.Attributes.GetValueOrDefault("ssn_tail");
            if (!string.IsNullOrEmpty(ssn_tail) && !string.IsNullOrEmpty(employee.ClockSeq))
            {
                var cseqTrim = employee.ClockSeq.TrimStart('0');
                employeeNumberAndPrinterAccessCode = cseqTrim + ssn_tail;
            }

            string samAccountName = FormatNewSamAccountName(employee.FirstName, employee.LastName);

            // for new hires, we NEVER want to use the work email provided by HR for samAccountName, but for existing employees
            // we need to use it, because it could capture things like the std email addr was taken so we used a different one
            if (!string.IsNullOrEmpty(employee.WorkEmail))
            {
                string workEmail = employee.WorkEmail.ToLower();
                bool isRockHillAddress = workEmail.Contains("@cityofrockhill");

                // HR may have fat fingered in a bad address, if so, skip and make our own
                if (isRockHillAddress)
                {
                    string[] emailParts = workEmail.Split('@');
                    samAccountName = emailParts[0];
                }
                else
                {
                    Log.Logger.Debug("Employee {id} has an erroneous work email address {email}, will not use for samAccountName.",
                        employee.Id, workEmail);
                }
            }

            var ssn = employee.Attributes.GetValueOrDefault("ssn");

            var dEntry = new EmployeeDirectoryEntry()
            {
                GivenName = firstNameToUse,
                SurName = lastNameToUse,
                MiddleName = middleName,
                Name = fullName,
                Active = employee.Active,
                BadgeNo = badgeNo,
                City = "Rock Hill",
                Comment = $"{firstNameToUse} {lastNameToUse}",
                Company = "City of Rock Hill",
                Department = employee.DeptName,
                DivisionCode = divisionCode,
                DivisionName = divisionName,
                Email = employee.WorkEmail,
                EmployeeNumber = employeeNumberAndPrinterAccessCode,
                EmployeeId = employee.ClockSeq?.PadLeft(10, '0'),
                ExtensionAttribute1 = cityMobile, // CustomText30 (City Cell Phone) -> Extension Attribute 1 -> city_mobile (no formatting)
                ExtensionAttribute3 = glcode.Head(7),
                ExtensionAttribute5 = cityPhoneFmt, //CustomText29 (City Desk Phone) -> Extension Attribute 5 -> city_phone
                ExtensionAttribute6 = agentPhone, //CustomText32 (Extension/AgentID) -> Extension Attribute 6 -> agent_phone
                ExtensionAttribute7 = currentTenureStart?.ToString("MM-dd-yyyy"),
                ExtensionAttribute10 = "", //"TAKE ACTION datetime MM-DD-YYYY:HH:SS",
                ExtensionAttribute11 = employee.Id,
                HireDate = currentTenureStart,
                OfficePhone = signPhoneFmt, //CustomText31 (Signature Number) -> Telephone Number -> sign_phone
                Manager = manager,
                MobilePhone = signMobileFmt,
                PostalCode = "29730",
                State = "SC",
                StreetAddress = employee.PrimaryWorkLocation,
                SamAccountName = samAccountName,
                Title = businessTitle,
                PersonalTitle = postNominals,
                PrimaryKey = employee.Id,
                ExternalId = ssn
            };

            if (!string.IsNullOrEmpty(managerName))
            {
                employee.Description = $"Reports to {managerName}";
            }

            if (employee.TermDate != null)
            {
                dEntry.ExtensionAttribute8 = employee.TermDate?.ToString("MM-dd-yyyy");
                dEntry.TermDate = employee.TermDate;
            }

            return dEntry;
        }

        public static bool ValidNewHire(Employee employee)
        {
            Log.Logger.Warning("Currently ignoring the pre-existance of a clockseq number on a new hire import");
            /*
            if (!string.IsNullOrEmpty(employee.ClockSeq))
            {
                Log.Logger.Error("Skipping existing employee record (id={0})", employee.Id);
                return false;
            }
            */

            if (string.IsNullOrEmpty(employee.LastName) ||
                string.IsNullOrEmpty(employee.FirstName))
            {
                Log.Logger.Error("First or last name missing, skipping employee record (id={0})", employee.Id);
                return false;
            }

            if (employee.HireDate == null && employee.RehireDate == null)
            {
                Log.Logger.Warning("Hire and Rehire dates null, SHOULD skip employee record (id={0})", employee.Id);
                return false;
            }

            var attrs = employee.Attributes;
            if (attrs == null)
            {
                Log.Logger.Warning("Missing required atttributes, skipping employee record (id={0})", employee.Id);
                return false;
            }

            var jobTitle = attrs.GetValueOrDefault("business_title");
            if (string.IsNullOrEmpty(jobTitle))
            {
                Log.Logger.Warning("Missing job title, skipping employee record (id={0})", employee.Id);
                return false;
            }

            var glCode = attrs.GetValueOrDefault("gl_code");
            if (string.IsNullOrEmpty(glCode))
            {
                Log.Logger.Warning("Missing glcode, skipping employee record (id={0})", employee.Id);
                return false;
            }

            string manager = attrs.GetValueOrDefault("manager_code");
            if (string.IsNullOrEmpty(manager))
            {
                Log.Logger.Warning("Missing manager, skipping employee record (id={0})", employee.Id);
                return false;
            }

            return true;
        }

        public static List<EmployeeDirectoryEntry> EmployeesToDirectoryEntries(IEnumerable<Employee> employees, Func<Employee, bool> filterFunc)
        {
            List<EmployeeDirectoryEntry> newEmployees = new List<EmployeeDirectoryEntry>();
            foreach (var employee in employees)
            {
                if (!filterFunc(employee)) continue;
                var dEntry = Converter.EmployeeToDirectoryEntry(employee);
                newEmployees.Add(dEntry);
            }

            return newEmployees;
        }
    }
}
