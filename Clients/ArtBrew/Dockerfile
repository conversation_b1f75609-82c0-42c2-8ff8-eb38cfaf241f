# Use the official .NET 8.0 runtime image as base
FROM mcr.microsoft.com/dotnet/runtime:8.0

# Build arguments for .NET tool distributions
ARG PAYROLL_DIST=dist/payroll
ARG TOAST_DIST=dist/toast
ARG CLIENT_DIST=dist/client

# Install the missing Perl module
RUN apt-get update && apt-get install -y libterm-readline-gnu-perl

# Set hostname to avoid "Name or service not known" error during ssmtp installation
RUN echo "payroll-tools" > /etc/hostname && \
    echo "127.0.0.1 localhost payroll-tools" > /etc/hosts && \
    echo "::1 localhost ip6-localhost ip6-loopback" >> /etc/hosts && \
    echo "ff02::1 ip6-allnodes" >> /etc/hosts && \
    echo "ff02::2 ip6-allrouters" >> /etc/hosts

# Create a fake hostname command that always succeeds to work around ssmtp post-install script
RUN mv /bin/hostname /bin/hostname.real && \
    echo '#!/bin/bash\necho "payroll-tools"' > /bin/hostname && \
    chmod +x /bin/hostname

# Now install ssmtp with the fake hostname command
RUN DEBIAN_FRONTEND=noninteractive apt-get install -y ssmtp mailutils procps

# Restore the real hostname command
RUN mv /bin/hostname.real /bin/hostname

# Install additional system packages required by the shell scripts
RUN apt-get update && apt-get install -y \
    # Basic utilities used in shell scripts
    coreutils \
    findutils \
    gzip \
    # SFTP client for file transfers
    lftp \
    # Clean up apt cache to reduce image size
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /opt/PayrollTools/bin

# Copy the Scripts directory and make shell scripts executable
COPY Clients/ArtBrew/Scripts/ .
RUN chmod +x *.sh

# Configure ssmtp to relay mail to host's postfix service
RUN echo "root=postmaster" > /etc/ssmtp/ssmtp.conf && \
    echo "mailhub=host.docker.internal:25" >> /etc/ssmtp/ssmtp.conf && \
    echo "rewriteDomain=artbrewventures.com" >> /etc/ssmtp/ssmtp.conf && \
    echo "FromLineOverride=YES" >> /etc/ssmtp/ssmtp.conf

# Copy all the built .NET tools from the dist directories
# These are built by the Makefile before running docker build
# Note: Paths are relative to the repository root since we'll build from there
COPY ${PAYROLL_DIST}/ ./payroll/
COPY ${TOAST_DIST}/ ./toast/
COPY ${CLIENT_DIST}/ ./client/

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Default command
CMD ["/bin/bash"]
