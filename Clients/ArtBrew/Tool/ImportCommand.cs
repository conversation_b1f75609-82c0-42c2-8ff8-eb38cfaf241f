﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.IO;
using System.Linq;
using System.Text;
using System.Xml;
using System.Xml.Serialization;
using Newtonsoft.Json;
using Payroll.Shared;
using Serilog;

namespace ArtBrew.Tool
{
    internal class EmployeeTotals : SortedDictionary<string, LocationTotals>
    {
    }

    internal class LocationTotals : SortedDictionary<string, JobCodeTotals>
    {
    }

    internal class JobCodeTotals : SortedDictionary<string, Earnings>
    {
    }

    internal class Earnings
    {
        public Earnings()
        {
            Hours = 0;
            Tips = 0;
            Overtime = 0;
        }

        public decimal Hours { get; set; }
        public decimal Tips { get; set; }
        public decimal Overtime { get; set; }
    }

    internal class ImportCommand
    {
        public ImportCommand()
        {
        }

        private string TranslateJobCode(string jobCode)
        {
            var translatedJobCode = Setting.Get("job_code_map", jobCode);
            if (string.IsNullOrEmpty(translatedJobCode))
                return jobCode;

            return translatedJobCode;
        }

        private DateTime ChargeDateFromPunch(DateTime dateTime)
        {
            var daysUntilSunday = ((int) DayOfWeek.Sunday - (int) dateTime.DayOfWeek + 7) % 7;
            return dateTime.AddDays(daysUntilSunday);
        }

        private void AddJobIfPresent(Employee employee, string name, string code, string rate)
        {
            if (string.IsNullOrEmpty(name)) return;
            var job = new Job()
            {
                Name = name,
                Code = code,
                Rate = Convert.ToDecimal(rate)
            };
            employee.Jobs.Add(job);
        }

        private void AddLocationIfPresent(Employee employee, string location)
        {
            if (string.IsNullOrEmpty(location)) return;

            var added = false;
            foreach (var job in employee.Jobs)
            {
                if (string.IsNullOrEmpty(job.Location))
                {
                    job.Location = location;
                    break;
                }
            }

            if (!added)
                Log.Logger.Error("Employee {id} has more locations than jobs {cnt}", employee.Id, employee.Jobs.Count);
        }

        private DateTime ParseUltiProDate(string date)
        {
            return DateTime.TryParseExact(date, "MMddyyyy", CultureInfo.CurrentCulture,
                DateTimeStyles.AssumeLocal, out DateTime timeOut) ? timeOut : DateTime.MinValue;
        }

        private bool ValidateXmlInput(string xmlContent)
        {
            if (string.IsNullOrEmpty(xmlContent))
            {
                Log.Logger.Error("XML input is null or empty");
                return false;
            }

            if (!xmlContent.TrimStart().StartsWith("<"))
            {
                Log.Logger.Error("XML input does not start with '<' - may not be valid XML");
                return false;
            }

            return true;
        }

        private string ExtractXmlSnippet(string xmlContent, int errorLine, int errorColumn, int maxLines = 50)
        {
            var lines = xmlContent.Split(new[] { "\r\n", "\r", "\n" }, StringSplitOptions.None);
            var startLine = Math.Max(0, errorLine - 10);
            var endLine = Math.Min(lines.Length - 1, errorLine + 10);

            var snippet = new StringBuilder();
            snippet.AppendLine($"XML Content (lines {startLine + 1}-{endLine + 1} of {lines.Length}):");
            snippet.AppendLine("```");

            for (var i = startLine; i <= endLine && i < lines.Length; i++)
            {
                var lineNumber = i + 1;
                var line = lines[i];

                if (lineNumber == errorLine)
                {
                    snippet.AppendLine($"{lineNumber,4}: {line}");
                    if (errorColumn > 0 && errorColumn <= line.Length)
                    {
                        var pointerLine = new string(' ', 6 + errorColumn - 1) + "^";
                        snippet.AppendLine(pointerLine);
                    }
                }
                else
                {
                    snippet.AppendLine($"{lineNumber,4}: {line}");
                }

                if (snippet.Length > 5000)
                {
                    snippet.AppendLine("... (truncated for length)");
                    break;
                }
            }

            snippet.AppendLine("```");
            return snippet.ToString();
        }

        private void LogEnhancedXmlError(Exception exception, string xmlContent)
        {
            Log.Logger.Fatal("=== XML DESERIALIZATION ERROR ===");

            if (exception is XmlException xmlException)
            {
                Log.Logger.Fatal("Error Position: Line {Line}, Column {Column}",
                    xmlException.LineNumber, xmlException.LinePosition);
                Log.Logger.Fatal("Source URI: {SourceUri}", xmlException.SourceUri ?? "N/A");

                if (!string.IsNullOrEmpty(xmlContent))
                {
                    var snippet = ExtractXmlSnippet(xmlContent, xmlException.LineNumber, xmlException.LinePosition);
                    Log.Logger.Fatal("XML Snippet:\n{Snippet}", snippet);
                }
            }
            else
            {
                Log.Logger.Fatal("Error Type: {Type}", exception.GetType().Name);
            }

            if (exception.InnerException != null)
            {
                Log.Logger.Fatal("Inner Exception: {Message}", exception.InnerException.Message);
                Log.Logger.Fatal("Inner Exception Type: {Type}", exception.InnerException.GetType().Name);
            }

            Log.Logger.Fatal("Exception Message: {Message}", exception.Message);
            Log.Logger.Fatal("Stack Trace: {StackTrace}", exception.StackTrace);
            Log.Logger.Fatal("=== END XML ERROR DETAILS ===");
        }

        public void Changes(List<string> args)
        {
            string xmlContent = null;
            try
            {
                var restaurantsById = Config.RestaurantsById();
                var employees = new List<Employee>();

                // Read XML content for validation and error reporting
                xmlContent = Console.In.ReadToEnd();
                Log.Logger.Debug("Read {Length} characters of XML content", xmlContent.Length);

                // Pre-deserialization validation
                var validationResults = new List<string>();
                if (ValidateXmlInput(xmlContent))
                {
                    validationResults.Add("✓ XML input validation passed");
                }
                else
                {
                    validationResults.Add("✗ XML input validation failed");
                }

                // Reset Console.In for deserialization
                var stringReader = new StringReader(xmlContent);
                Console.SetIn(stringReader);

                var serializer = new XmlSerializer(typeof(RECORDS));
                var records = (RECORDS)serializer.Deserialize(Console.In);
                Log.Logger.Debug("Parsing {x} employees", records.Employees.Count);

                foreach (var ultiProEmployee in records.Employees)
                {
                    var employee = new Employee()
                    {
                        Id = ultiProEmployee.ULTIPROEENUMBER,
                        OrgCode = ultiProEmployee.COMPANYCD,
                        FirstName = ultiProEmployee.FIRSTNAME,
                        LastName = ultiProEmployee.LASTNAME,
                        StreetAddress = ultiProEmployee.ADDRESS1,
                        CityAddress = ultiProEmployee.CITY,
                        State = ultiProEmployee.STATE,
                        Zip = ultiProEmployee.ZIP,
                        WorkEmail = ultiProEmployee.PRIMARYEMAILADDRESS,
                        ClockSeq = ultiProEmployee.ULTIPROEENUMBER,
                        CellPhone = ultiProEmployee.PHONE,
                        // the emplstatus here could be A for active, L for on leave (we treat as active), and T for terminated
                        Active = ultiProEmployee.EMPLSTATUS != "T",
                        HireDate = ParseUltiProDate(ultiProEmployee.DATEHIRED),
                        TermDate = ParseUltiProDate(ultiProEmployee.TERMINATIONDATE),
                        Jobs = new List<Job>()
                    };

                    if (restaurantsById.ContainsKey(ultiProEmployee.STORENUMBER1))
                        employee.PrimaryWorkLocation = restaurantsById[ultiProEmployee.STORENUMBER1];

                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME1, ultiProEmployee.JOBNUMBER1,
                        ultiProEmployee.RATE1);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME2, ultiProEmployee.JOBNUMBER2,
                        ultiProEmployee.RATE2);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME3, ultiProEmployee.JOBNUMBER3,
                        ultiProEmployee.RATE3);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME4, ultiProEmployee.JOBNUMBER4,
                        ultiProEmployee.RATE4);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME5, ultiProEmployee.JOBNUMBER5,
                        ultiProEmployee.RATE5);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME6, ultiProEmployee.JOBNUMBER6,
                        ultiProEmployee.RATE6);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME7, ultiProEmployee.JOBNUMBER7,
                        ultiProEmployee.RATE7);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME8, ultiProEmployee.JOBNUMBER8,
                        ultiProEmployee.RATE8);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME9, ultiProEmployee.JOBNUMBER9,
                        ultiProEmployee.RATE9);
                    AddJobIfPresent(employee, ultiProEmployee.JOBNAME10, ultiProEmployee.JOBNUMBER10,
                        ultiProEmployee.RATE10);

                    AddLocationIfPresent(employee, ultiProEmployee.STORENUMBER2);
                    employees.Add(employee);
                }

                var sortedEmployees = employees.OrderBy(x => x.PrimaryWorkLocation);
                ConsoleService.PrintFormattedJson(sortedEmployees);
            }
            catch (Exception e)
            {
                LogEnhancedXmlError(e, xmlContent);
            }
        }

        public void Punches(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: ArtBrew.Tool.exe import punches <restaurant>");
                Console.WriteLine("  where: <restaurant> is the restaurant location code like BRMillsRiver");
                return;
            }

            try
            {
                var json = Console.In.ReadToEnd();

                // get the tip scheme
                var restaurantAbbrev = args[0].Trim();
                var tipScheme = Config.TipSchemeForRestaurant(restaurantAbbrev);
                Log.Logger.Information("Using tip scheme: {scheme}, for restaurant code: {code}", tipScheme == "declared" ? "declared" : "total", restaurantAbbrev);

                var punchPairs = JsonConvert.DeserializeObject<List<PunchPair>>(json);
                var glCodes = Config.GlCodes();
                var locationCodes = Config.LocationCodes();

                if (punchPairs == null || punchPairs.Count == 0) return;
                // the last date in the file
                var chargeDate = ChargeDateFromPunch(punchPairs[^1].Date.DateTime);

                // import tool is sensitive to preceding spaces in these column headers (dont add)
                Console.WriteLine(
                    "EE Number, Charge Date, Earning Code, Earning Hours, Earning Amount, Piece Count, Shift Code, Location, Job Code,Project, Org Level 1, Org Level 2, Org Level 3,Org Level 4");
                var employeeTotals = new EmployeeTotals();
                foreach (var punch in punchPairs)
                {
                    if (!employeeTotals.ContainsKey(punch.ClockSeq))
                        employeeTotals.Add(punch.ClockSeq, new LocationTotals());

                    var locationTotal = employeeTotals[punch.ClockSeq];
                    if (!locationTotal.ContainsKey(punch.Location))
                        locationTotal.Add(punch.Location, new JobCodeTotals());

                    var jobCodeTotals = locationTotal[punch.Location];
                    if (!jobCodeTotals.ContainsKey(punch.JobCode)) jobCodeTotals.Add(punch.JobCode, new Earnings());

                    var earnings = jobCodeTotals[punch.JobCode];

                    // tip scheme allows you to override the default (which is called "total")
                    // the declared scheme means just the declared cash tips as seen below
                    if (tipScheme == "declared")
                        earnings.Tips += punch.CashTip + punch.ExceptionPay; // exception pay is non-cash gratuity service charges in toast land
                    else
                        earnings.Tips += punch.CashTip + punch.NonCashTip;

                    earnings.Hours += punch.Hours;
                    earnings.Overtime += punch.Overtime;
                }

                foreach (var (eeCode, locationTotals) in employeeTotals)
                foreach (var (location, jobCodeTotals) in locationTotals)
                foreach (var (jobCode, earnings) in jobCodeTotals)
                {
                    // REG = Regular Hours
                    var hours = earnings.Hours.ToString("0.########");
                    var glCode = glCodes.TryGetValue(location, out var resA) ? resA : string.Empty;
                    var ukgLocation = locationCodes.TryGetValue(location, out var resB) ? resB : string.Empty;

                    Console.WriteLine(
                        $"{eeCode},{chargeDate.ToShortDateString()},REG,{hours},0,,,{ukgLocation},{jobCode},,,,,{glCode}");
                    // OT = Overtime Hours
                    var overtime = earnings.Overtime.ToString("0.########");
                    Console.WriteLine(
                        $"{eeCode},{chargeDate.ToShortDateString()},OT,{overtime},0,,,{ukgLocation},{jobCode},,,,,{glCode}");

                    // TPCR = Tips Charged
                    var tips = earnings.Tips.ToString("F");
                    var tipCode = Config.TipCodeForLocation(location);

                    Console.WriteLine(
                        $"{eeCode},{chargeDate.ToShortDateString()},{tipCode},0,{tips},,,{ukgLocation},{jobCode},,,,,{glCode}");
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }
    }
}
