# Upward Projects Payroll Integration System
## Developer Onboarding Guide

### Table of Contents
1. [System Overview](#system-overview)
2. [Architecture Components](#architecture-components)
3. [Data Flow](#data-flow)
4. [Scheduled Operations](#scheduled-operations)
5. [Configuration Management](#configuration-management)
6. [Error Handling & Monitoring](#error-handling--monitoring)
7. [Development Environment](#development-environment)
8. [Troubleshooting Guide](#troubleshooting-guide)

---

## System Overview

The Upward Projects Payroll Integration System is an automated employee lifecycle management platform that synchronizes employee data between **Paylocity** (HR/Payroll system) and **Toast** (POS/Restaurant management system) for a multi-location restaurant group.

### Business Context
- **Client**: Upward Projects (restaurant group with 25+ locations)
- **Primary Systems**: Paylocity ↔ LiteDB ↔ Toast
- **Purpose**: Automated employee onboarding, updates, and terminations
- **Locations**: Multiple Postino, Joyride, Federal Pizza, and Windsor Churn locations

### Key Operations
- **Employee Synchronization**: 4 times daily (midnight, 6am, noon, 6pm)
- **Exception Reporting**: Weekdays at 2:22am
- **Account Validation**: Sundays at 4am

---

## Architecture Components

```mermaid
graph TB
    subgraph "External Systems"
        A[Paylocity API<br/>HR/Payroll System]
        B[Toast API<br/>POS System]
        C[SendGrid<br/>Email Service]
    end
    
    subgraph "Core Platform"
        D[LiteDB<br/>Local Cache]
        E[.NET Tools<br/>Processing Engine]
        F[Shell Scripts<br/>Orchestration]
    end
    
    subgraph "Infrastructure"
        G[Cron Jobs<br/>Scheduling]
        H[Log Files<br/>Monitoring]
        I[Email Notifications<br/>Alerting]
    end
    
    A --> D
    D --> B
    E --> D
    F --> E
    G --> F
    E --> H
    E --> I
    I --> C
```

### Core Components

#### 1. **Data Storage**
- **LiteDB**: Local embedded database for caching employee data
- **File System**: JSON exports, logs, and temporary files
- **Configuration**: INI files for system settings

#### 2. **Processing Tools** (.NET Core)
- [`Paylocity.Tool.dll`](../../Paylocity.Tool/): Paylocity API integration
- [`Toast.Tool.dll`](../../Toast.Tool/): Toast API integration  
- [`Payroll.Tool.dll`](../../Payroll.Tool/): Core processing and caching
- [`Upward.Tool.dll`](Upward.Tool/): Client-specific comparison logic

#### 3. **Orchestration Scripts**
- [`sync-to-toast.sh`](Scripts/sync-to-toast.sh): Main synchronization workflow
- [`report-exceptions.sh`](Scripts/report-exceptions.sh): Exception monitoring
- [`validate-toast-accounts.sh`](Scripts/validate-toast-accounts.sh): Account validation

---

## Data Flow

### Primary Synchronization Flow

```mermaid
sequenceDiagram
    participant P as Paylocity API
    participant L as LiteDB Cache
    participant T as Toast API
    participant E as Email System
    
    Note over P,E: Runs 4x daily (0,6,12,18 hours)
    
    P->>L: Export employee data
    Note right of L: Cache import/update
    
    L->>T: Process terminations
    T->>E: Send termination results
    
    L->>T: Process new hires
    T->>E: Send hire results
    
    L->>T: Sync existing employees
    T->>E: Send sync results
    
    Note over L: Compress and archive data
```

### Exception Monitoring Flow

```mermaid
sequenceDiagram
    participant T as Toast API
    participant U as Upward.Tool
    participant L as Log System
    participant E as Email System
    
    Note over T,E: Runs weekdays at 2:22am
    
    T->>U: Export employee data
    U->>L: Compare and log exceptions
    L->>E: Email exception report
    E->>E: <NAME_EMAIL>
    E->>E: <NAME_EMAIL>
```

---

## Scheduled Operations

### Cron Schedule Analysis

| Time | Frequency | Script | Purpose |
|------|-----------|--------|---------|
| `0 0,6,12,18 * * *` | 4x daily | [`sync-to-toast.sh`](Scripts/sync-to-toast.sh) | Full employee synchronization |
| `22 2 * * 1-5` | Weekdays | [`report-exceptions.sh`](Scripts/report-exceptions.sh) | Exception monitoring |
| `0 4 * * 0` | Sundays | [`validate-toast-accounts.sh`](Scripts/validate-toast-accounts.sh) | Account validation |

### Detailed Operation Breakdown

#### 1. **Employee Synchronization** (`sync-to-toast.sh`)
**Frequency**: Every 6 hours (midnight, 6am, noon, 6pm)

**Process Flow**:
1. **Log Rotation**: Maintain last 5000 lines of daily logs
2. **Data Export**: Pull employee data from Paylocity API
3. **Cache Update**: Import data into LiteDB cache
4. **Termination Processing**: 
   - Extract terminated employees
   - Send to Toast API
   - Email results
5. **New Hire Processing**:
   - Extract active employees (new hires)
   - Send to Toast API with hire limits
   - Email results
6. **Employee Updates**:
   - Sync existing employee changes
   - Apply sync limits for API throttling
   - Email results
7. **Cleanup**: Archive and compress data files

#### 2. **Exception Reporting** (`report-exceptions.sh`)
**Frequency**: Weekdays at 2:22am

**Process Flow**:
1. Export current Toast employee data
2. Compare against expected data using [`Upward.Tool`](Upward.Tool/)
3. Generate exception log with JSON formatting
4. Extract exceptions and create CSV report
5. Email reports to management and payroll teams

#### 3. **Account Validation** (`validate-toast-accounts.sh`)
**Frequency**: Sundays at 4am

**Process Flow**:
1. Export active employees from cache
2. Validate against Toast system
3. Generate validation report
4. Email results

---

## Configuration Management

### Settings Structure ([`settings.ini`](Scripts/settings.ini))

#### System Configuration
```ini
[system]
dsn = mongodb+srv://... # Legacy - now uses LiteDB
dbname = ptools
smtp_host = smtp.sendgrid.com
notify_addr = <EMAIL>
```

#### Paylocity Integration
```ini
[paylocity]
username = paylocity-upward
host = api.paylocity.com
companyid = 83831
sync_start_year = 2024
```

#### Toast Integration
```ini
[toast]
clientid = <hidden>
groupid = <hidden>
host = ws-api.toasttab.com
mode = execute
sync_limit = 25
hire_limit = 5
term_limit = 5
```

### Key Configuration Sections

#### Restaurant Mappings
- **25+ locations** mapped between Toast GUIDs and internal location IDs
- Examples: Postino locations, Joyride, Federal Pizza, Windsor Churn

#### Job Code Translations
- **Paylocity job codes** (numeric) → **Toast job GUIDs**
- Covers all positions: FOH, BOH, Management, Training roles
- Skip lists for deprecated or invalid job codes

#### Operational Modes
- **Execute vs. Dry Run**: Different modes for testing vs. production
- **Rate Limiting**: API call limits to prevent throttling
- **Skip Lists**: Employees, jobs, and locations to exclude

---

## Error Handling & Monitoring

### Logging Strategy

#### Log File Organization
```
../Logs/
├── Toast/
│   ├── terms-{day}.log      # Termination processing
│   ├── hires-{day}.log      # New hire processing  
│   ├── sync-{day}.log       # Employee sync processing
│   └── exceptions-{day}.log # Exception monitoring
├── Paylocity/
│   └── payloc-{day}-{hour}.log # Paylocity API calls
└── fulldump-{day}.json.gz   # Archived data exports
```

#### Log Rotation
- **Automatic rotation**: Keep last 5000 lines per log file
- **Daily archiving**: Compress and store full data exports
- **Retention**: Logs organized by day for easy troubleshooting

### Email Notification System

#### Notification Types
1. **Operation Results**: Success/failure for each sync operation
2. **Exception Reports**: Daily exception summaries (weekdays)
3. **Validation Reports**: Weekly account validation results

#### Recipients
- **IT Admin**: `<EMAIL>`
- **Development**: `<EMAIL>`
- **Management**: `<EMAIL>`
- **Payroll**: `<EMAIL>`

### Error Categories

#### 1. **API Errors**
- **Paylocity**: Authentication, rate limiting, data format issues
- **Toast**: Restaurant access, job code mismatches, employee conflicts

#### 2. **Data Validation Errors**
- **Missing Mappings**: Unmapped job codes or locations
- **Data Inconsistencies**: Employee data mismatches between systems
- **Business Rule Violations**: Invalid hire dates, duplicate employees

#### 3. **System Errors**
- **Database Issues**: LiteDB connection or corruption
- **File System**: Disk space, permission issues
- **Network**: API connectivity, timeout issues

---

## Development Environment

### Prerequisites

#### Required Software
- **.NET 8.0 SDK**: For building and running tools
- **LiteDB**: Embedded database (included in .NET tools)
- **Shell Environment**: Bash-compatible shell for scripts

#### API Access Requirements
- **Paylocity API**: Username, password, client credentials
- **Toast API**: Client ID, secret, group ID access
- **SendGrid**: API key for email notifications

### Local Development Setup

#### 1. **Build and Publish**
```bash
make upwards && publish-upwards
```

#### 2. **Configuration**
- Copy [`settings.ini`](Scripts/settings.ini) template
- Update with development API credentials
- Set appropriate mode flags (`dryrun` vs `execute`)

#### 3. **Testing**
```bash
# Test Paylocity connection
dotnet ../../Paylocity.Tool/bin/Debug/net8.0/Paylocity.Tool.dll export employees

# Test Toast connection  
dotnet ../../Toast.Tool/bin/Debug/net8.0/Toast.Tool.dll export employees

# Test cache operations
dotnet ../../Payroll.Tool/bin/Debug/net8.0/Payroll.Tool.dll cache dump active
```

### Development Workflow

#### 1. **Making Changes**
- Modify .NET tools in respective directories
- Update configuration in [`settings.ini`](Scripts/settings.ini)
- Test with `dryrun` mode first

#### 2. **Testing Scripts**
```bash
# Test sync process (dry run)
./Scripts/sync-to-toast.sh

# Test exception reporting
./Scripts/report-exceptions.sh

# Test validation
./Scripts/validate-toast-accounts.sh
```

#### 3. **Deployment**
- Build tools: `make` in each tool directory
- Update production [`settings.ini`](Scripts/settings.ini)
- Deploy to `/opt/openarc/PayrollTools/`

---

## Troubleshooting Guide

### Common Issues

#### 1. **Sync Failures**

**Symptoms**: Email notifications showing sync errors, missing employees in Toast

**Diagnosis**:
```bash
# Check recent logs
tail -100 ../Logs/Toast/sync-$(date +%d).log

# Check Paylocity connection
dotnet paylocity/Paylocity.Tool.dll export employees

# Check LiteDB cache
dotnet payroll/Payroll.Tool.dll cache dump active | head -10
```

**Common Causes**:
- API credential expiration
- Rate limiting (too many requests)
- Job code mapping issues
- Restaurant access permissions

#### 2. **Exception Reports**

**Symptoms**: High number of exceptions in daily reports

**Diagnosis**:
```bash
# Review exception log
cat ../Logs/Toast/exceptions-$(date +%d).log | grep ERROR

# Compare employee data
dotnet client/Upward.Tool.dll compare employees
```

**Common Causes**:
- New job codes not mapped
- Employee data inconsistencies
- Toast restaurant configuration changes

#### 3. **Email Delivery Issues**

**Symptoms**: Missing notification emails

**Diagnosis**:
- Check SendGrid API key validity
- Verify recipient email addresses
- Review SMTP configuration in [`settings.ini`](Scripts/settings.ini)

### Emergency Procedures

#### 1. **Stop Automated Processing**
```bash
# Disable cron jobs temporarily
crontab -e
# Comment out Upward-related entries
```

#### 2. **Manual Data Recovery**
```bash
# Export current state
dotnet paylocity/Paylocity.Tool.dll export employees > backup-$(date +%Y%m%d).json

# Restore from backup
cat backup-YYYYMMDD.json | dotnet payroll/Payroll.Tool.dll cache import
```

#### 3. **Contact Escalation**
- **Level 1**: IT Admin (`<EMAIL>`)
- **Level 2**: Development (`<EMAIL>`)
- **Level 3**: Management (`<EMAIL>`)

### Performance Monitoring

#### Key Metrics
- **Sync Success Rate**: Should be >95%
- **API Response Times**: Paylocity <5s, Toast <10s
- **Exception Count**: Should be <10 per day
- **Email Delivery**: All notifications should arrive within 5 minutes

#### Monitoring Commands
```bash
# Check recent sync performance
grep "Processing" ../Logs/Toast/sync-$(date +%d).log

# Monitor API response times
grep "Response time" ../Logs/Paylocity/payloc-$(date +%d)-*.log

# Count daily exceptions
wc -l ../Logs/Toast/exceptions-$(date +%d).log
```

---

## System Maintenance

### Regular Maintenance Tasks

#### Weekly
- Review exception reports for trends
- Check log file sizes and cleanup if needed
- Verify email notification delivery

#### Monthly  
- Review API usage and rate limiting
- Update job code mappings for new positions
- Check restaurant configuration changes

#### Quarterly
- Review and update API credentials
- Performance analysis and optimization
- Documentation updates

### Configuration Updates

#### Adding New Restaurants
1. Get Toast restaurant GUID from Toast admin
2. Add mapping to `[toast_restaurants]` section
3. Test with validation script
4. Update skip lists if needed

#### Adding New Job Codes
1. Get Paylocity job code and Toast job GUID
2. Add mapping to `[toast_jobs]` section  
3. Test with dry run sync
4. Monitor for exceptions

### Security Considerations

#### Credential Management
- API keys stored in [`settings.ini`](Scripts/settings.ini) - **restrict file permissions**
- Regular credential rotation (quarterly)
- Monitor for unauthorized API access

#### Data Protection
- Employee data cached locally in LiteDB
- Automatic cleanup and archiving
- Secure email transmission via SendGrid

---

## Additional Resources

### Related Documentation
- [Toast API Documentation](https://doc.toasttab.com/)
- [Paylocity API Documentation](https://www.paylocity.com/api/)
- [LiteDB Documentation](https://www.litedb.org/)

### Tool-Specific Documentation
- [`Paylocity.Tool`](../../Paylocity.Tool/): Paylocity API integration
- [`Toast.Tool`](../../Toast.Tool/): Toast API integration
- [`Payroll.Tool`](../../Payroll.Tool/): Core caching and processing
- [`Upward.Tool`](Upward.Tool/): Client-specific comparison logic

### Support Contacts
- **Technical Issues**: `<EMAIL>`
- **Business Issues**: `<EMAIL>`
- **Payroll Questions**: `<EMAIL>`

---

*Last Updated: July 2025*
*System Version: Production*