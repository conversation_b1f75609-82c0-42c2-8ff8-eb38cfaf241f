#!/bin/sh
echo "hello world"

echo "will report exceptions to joe<PERSON><PERSON>@openarc.net in 10 seconds..."
sleep 10s

DOM=`date +%d`
HOUR=`date +%H`
TODAY=`date +day%d-hr%H`
SDATE=`date +%D`

echo Exporting Toast employee data
dotnet toast/Toast.Tool.dll export employees > ../employees.json
cat ../employees.json| LOGJSON=1 LOGFILE=../Logs/Toast/exceptions-$DOM.log dotnet client/Upward.Tool.dll compare employees
cat ../Logs/Toast/exceptions-$DOM.log | dotnet payroll/Payroll.Tool.dll json grep "@mt" > /tmp/exceptions-$DOM.csv
echo "Please find attached the exception report for $SDATE" | dotnet payroll/Payroll.Tool.dll mail attachment "Exception Report for $SDATE" /tmp/exceptions-$DOM.csv <EMAIL>
