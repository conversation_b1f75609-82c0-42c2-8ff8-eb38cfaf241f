[system]
dsn = mongodb+srv://upwards:<EMAIL>
dbname = ptools
smtp_host = smtp.sendgrid.com
smtp_user = apikey
smtp_pwd = *********************************************************************
notify_addr = <EMAIL>
notify_addr2 = <EMAIL>
from_addr = <EMAIL>
email_subject_errors = Cash Deposit Audit Failure
email_body_errors = /usr/share/PayrollTools/errors.mail.template

[paylocity]
username = paylocity-upward
password = ju$t3n0ughtoBeStrong86
host = api.paylocity.com
companyid = 83831
clientid = 5J/1qVznfkWg1LE/Z1Qsoi04NTg1MjIwMjYwMTIwOTc2MzIw
secret = jU5bp36Z5GGoReYcKEyUF0ATQ/jIYyVVbzxwpMV0PHqYVooyg1b+kBnYyy0hE0272cKXIDZ5x5D2HFtcvOJsJg==
prior_secret_no_longer_used = pFeO/EbyfobTzUKayigCWqA4Gc+mFe3qGCKiBl4uEUjpof2eHCaZXOmUF/QLkhRqo5gidDKjjHQ5tolkEP2wvQ==

[paylocity_skip_locations]
100 = corporate office
118 = permenantly closed

[paylocity_fields]
job_code_old = job
job_code = costcenter3
fname = preferred

[toast]
clientid = 9Tdt7q588X7y2BH7XTOaUPzAYvxAm6Kc
secret = OYUG9qFeNHx9_ldQPIyM3jWYX9_VV5aKb_LLsq4T5gZtcA3uMsibSs6ZazvkD44C
groupid = 801aea3a-4c07-42f6-aca7-1d8cabac35c6
host = ws-api.toasttab.com
defaultid = 6d2c8b3b-e330-4b85-9441-34e17bc191f9
delay = 1
sandhost = ws-sandbox-api.eng.toasttab.com
mode = dryrun
rm_jobs_mode = dryrun
hire_mode=execute
sync_mode=execute
term_mode=execute
sync_limit = 250
hire_limit = 50
hire_year_min = 2025
term_limit = 50

[toast_restaurants]
#Postino Arcadia
6d2c8b3b-e330-4b85-9441-34e17bc191f9 = 101
#Postino Central
d66fe635-2bd2-4899-b9b0-8cdcea0644f1 = 102
#Windsor Churn
d0d02e40-3c52-4ab6-ab9a-57ca0b7e44d7=103
#Postino East
866a24c6-8e7c-488d-8327-9a73082e35e3 = 104
#Federal Pizza
bbbb9bc1-71c8-46a1-b5ff-c12e87c02ff7=105
#Joyride East
b954eb7a-92c9-43c4-95ec-b12113fd226b=106
#Joyride Central
2677725d-fdf8-4f86-8450-65291b212556=107
#Postino Annex
ad9f7c50-4694-46bf-b814-08591dc2cff5 = 108
#Postino LoHi
517f7067-3e97-406b-9b7b-0cfc0234eff8 = 109
#Postino Kierland
f095feaf-7f94-4dab-a93a-909d12cfa9f9 = 110
#Postino Highland
cb3e7ac4-b45d-4576-9dfa-290a3f720ede = 111
#Postino Heights
90cf19dd-7f18-4237-b675-102a1a2f9e44=112
#Postino Montrose
7f5d7f60-a7b2-40df-9386-242820d0938f=113
#Postino Broadway
79428014-57ac-4a62-92af-a2a2b5c235af=114
#Postino 9CO
2ec0a9d6-9911-4e87-8768-b832a7f9dcfe=115
#Postino Grant
d7cacdff-1ecf-4bd8-9986-0f33c9e2ff4a = 116
#Postino South Tempe
2b81ad59-db1f-4980-9031-a39946118b09 = 117
#Postino Town & Country
626e0f72-4e00-40d6-ab4e-b3e7a971fa59=119
#Postino Uptown Park
e414314d-e79f-4c03-85bb-d0f912f03270=120
#Postino Cinco Ranch
d304a5ff-09a1-4a07-96f8-68d7f4b8eefc=123
#Postino Addison
a8043d2c-bb25-433c-8d61-61fff6ee4438=124
#Postino Westminster
0581075f-1a26-4ad8-8e67-c3e55aadaffa = 127
# Postino Park Place
ac99d021-bb42-48d0-bd47-5942a54249a1=128
#Postino The Woodlands
dc707203-2ba6-4bbb-a520-46a64ab521f4=129
#Postino Rim
69ff2a03-c691-42d3-a23c-eb4055397d48=130
#Postino Boulder
b4f930e2-70a4-4f7e-9d06-ccbbe7df80be=125
#Postino HIghlands Ranch
351e875d-8cec-40b1-b8b1-414341435f2d=121
#Positno West Midtown in Georgia
4e212b8c-ed59-4b5c-af8d-1c7d378199a2=126
#Postino Buckhead
4e9534dc-1b82-4126-a687-102c2f5cd988=122
#Postino South Broadway
11e1852c-2812-4f77-8c7e-7d114f55df8f=131
#Postino South Lamar
753b446b-79ad-4e4e-8aea-4ce4b71a83f9=132
#Postino Village District
05c3a135-2ec8-42f0-b47b-aad4b6a61b76=133
#Postino Southlake
cc6320b5-e38e-4ce5-9432-e19ee2a00cfd=134
#Postino Little Italy
bffec8f1-bc65-4203-92b0-b4e96cd14244=135
#Postino Del Mar
1cbb5315-7e89-4947-91b0-4e775a4125c8=136
#Postino Ballantyne
879dbfa4-fc4b-4556-8968-2b4c09e08ee2=137
#Postino 12 South
965c42fd-cc0f-4637-afd2-576c4e710eea=138
#Postino Bryker Woods
f2823120-7c7a-4361-a369-9325e713f795 = 139
#Postino Peoria
47ec668a-c04f-4226-8aaf-8c599eed365a=140
# Federal Pizza Paradise Valley
b9344493-d72d-4b32-b899-9a9501fe7c72=141

[toast_skip_jobs]
10200 = Not found 1
10021 = Not found 2

[toast_skip_wage_overrides]
c278480e-cffe-4842-9cc7-041d57b01db5 = BOH in TRN
dcfc957d-142f-48d7-b352-3a1d810d6322 = BOH CTR
00860584-b876-40a1-9136-3f2ef8431171 = BOH MEET

[toast_skip_employees]
<EMAIL> = Ashlyn Poly
<EMAIL> = Monica Menchaca
<EMAIL> = Trish Dalrymple
<EMAIL> = Carlos Buscaglia
<EMAIL> = Claudia Galas
<EMAIL> = Brent Renner
<EMAIL> = Ari Mangual
<EMAIL> = Halley McCarthy
<EMAIL> = IT Admin
<EMAIL> = Felix Kaminski
<EMAIL> = Tanni Rednor
<EMAIL> = DoorDash
<EMAIL> = Cory Lattuca
<EMAIL> = Brent Karlicek
<EMAIL> = Colleen Power
<EMAIL> = QA Ingest
<EMAIL> = Phillip Bock
<EMAIL> = Cathryn Kerkman
<EMAIL> = Blake Deffenbacher
<EMAIL> = Luke Christopherson
<EMAIL> = Sarah Hart
<EMAIL> = Rosana DeMar
<EMAIL> = Dro Gonzalez
<EMAIL> = Nina Camay
<EMAIL> = Juba Malou
<EMAIL> = Sarah Pak
<EMAIL> = Kenneth Hoffman
<EMAIL> = Marissa Travis
<EMAIL> = Michael Magdaleno
<EMAIL> = Michael Wang
<EMAIL> = Brian Anderson

[toast_fields]
email = personal
passcode = clockseq

[toast_manager_jobs]
ce2b21d5-dfc2-4b55-8bde-9cad8aba9d16 = GM
b4d6062b-81da-4c1a-ac11-86c2b75861e2 = AGM
f1643651-2152-4066-b1f4-b7c4b8b8f526 = RM
e324f64e-b89c-43ba-bb3b-811b2d3f9d75 = EC
9054ca88-0280-44b2-878d-b351bf53a63f = SC
b0275d73-05f2-42eb-a92b-5b38ca05fb33 = ESC

[toast_administrative_jobs]
f075544d-bdce-4165-bc07-391e4e18d5cb = Report Access

[toast_restaurant_contact]
# Postino Arcadia
6d2c8b3b-e330-4b85-9441-34e17bc191f9 = <EMAIL>
# Postino Central
d66fe635-2bd2-4899-b9b0-8cdcea0644f1 = <EMAIL>
# Windsor
d0d02e40-3c52-4ab6-ab9a-57ca0b7e44d7 = <EMAIL>
# Postino East
866a24c6-8e7c-488d-8327-9a73082e35e3 = <EMAIL>
# Federal Pizza
bbbb9bc1-71c8-46a1-b5ff-c12e87c02ff7 = <EMAIL>
# Joyride East
b954eb7a-92c9-43c4-95ec-b12113fd226b = <EMAIL>
# Joyride Central
2677725d-fdf8-4f86-8450-65291b212556 = <EMAIL>
# Postino Annex
ad9f7c50-4694-46bf-b814-08591dc2cff5 = <EMAIL>
# Postino Lohi
517f7067-3e97-406b-9b7b-0cfc0234eff8 = <EMAIL>
# Postino Kierland
f095feaf-7f94-4dab-a93a-909d12cfa9f9 = <EMAIL>
# Postino Highland
cb3e7ac4-b45d-4576-9dfa-290a3f720ede = <EMAIL>
# Postino Heights
90cf19dd-7f18-4237-b675-102a1a2f9e44 = <EMAIL>
# Postino Montrose
7f5d7f60-a7b2-40df-9386-242820d0938f = <EMAIL>
# Postino Broadway
79428014-57ac-4a62-92af-a2a2b5c235af = <EMAIL>
# Postino 9CO
2ec0a9d6-9911-4e87-8768-b832a7f9dcfe = <EMAIL>
# Postino Grant
d7cacdff-1ecf-4bd8-9986-0f33c9e2ff4a = <EMAIL>
# Postino South Tempe
2b81ad59-db1f-4980-9031-a39946118b09 = <EMAIL>
# Postino Town & Country
626e0f72-4e00-40d6-ab4e-b3e7a971fa59 = <EMAIL>
# Postino Uptown Park
e414314d-e79f-4c03-85bb-d0f912f03270 = <EMAIL>
# Postino Cinco Ranch
d304a5ff-09a1-4a07-96f8-68d7f4b8eefc = <EMAIL>
# Postino Buckhead
4e9534dc-1b82-4126-a687-102c2f5cd988 = <EMAIL>
# Postino Highlands Ranch
351e875d-8cec-40b1-b8b1-414341435f2d = <EMAIL>
# Postino Park Place
ac99d021-bb42-48d0-bd47-5942a54249a1 = <EMAIL>
# Postino Boulder
b4f930e2-70a4-4f7e-9d06-ccbbe7df80be = <EMAIL>
# Postino The Woodlands
dc707203-2ba6-4bbb-a520-46a64ab521f4 = <EMAIL>
# Postino The Rim
69ff2a03-c691-42d3-a23c-eb4055397d48 = <EMAIL>
# Postino Addison
a8043d2c-bb25-433c-8d61-61fff6ee4438 = <EMAIL>
# Postino Southlake
cc6320b5-e38e-4ce5-9432-e19ee2a00cfd = <EMAIL>
# Positno Little Italy
bffec8f1-bc65-4203-92b0-b4e96cd14244 = <EMAIL>
# Postino Del Mar
1cbb5315-7e89-4947-91b0-4e775a4125c8 = <EMAIL>
# Postino South Broadway
11e1852c-2812-4f77-8c7e-7d114f55df8f = <EMAIL>
# Postino South Lamar
753b446b-79ad-4e4e-8aea-4ce4b71a83f9 = <EMAIL>
# Positno Village District
05c3a135-2ec8-42f0-b47b-aad4b6a61b76 = <EMAIL>
# Postino Ballantyne
879dbfa4-fc4b-4556-8968-2b4c09e08ee2 = <EMAIL>
# Postino 12 South
965c42fd-cc0f-4637-afd2-576c4e710eea = <EMAIL>
# Postino Peoria
47ec668a-c04f-4226-8aaf-8c599eed365a = <EMAIL>
# Postino Bryker Woods
f2823120-7c7a-4361-a369-9325e713f795 = <EMAIL>
# Postino Westminster
0581075f-1a26-4ad8-8e67-c3e55aadaffa = <EMAIL>
# Federal Pizza Paradise Valley
b9344493-d72d-4b32-b899-9a9501fe7c72 = <EMAIL>
