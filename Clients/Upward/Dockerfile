# Use the official .NET 8.0 runtime image as base
FROM mcr.microsoft.com/dotnet/runtime:8.0

# Build arguments for .NET tool distributions
ARG PAYROLL_DIST=dist/payroll
ARG TOAST_DIST=dist/toast
ARG PAYLOCITY_DIST=dist/paylocity
ARG CLIENT_DIST=dist/client

# Install additional system packages required by the shell scripts
RUN apt-get update && apt-get install -y \
    # Basic utilities used in shell scripts
    coreutils \
    findutils \
    gzip \
    # SFTP client for file transfers
    lftp \
    # Clean up apt cache to reduce image size
    && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /opt/openarc/Prod

# Declare volumes for host mounts
# These directories will be mounted from the host OS
VOLUME ["/opt/openarc/Logs", "/opt/openarc/Mailbox", "/usr/share/PayrollTools/"]

# Copy the Scripts directory and make shell scripts executable
COPY Clients/Upward/Scripts/ .
RUN chmod +x *.sh

# Copy all the built .NET tools from the dist directories
# These are built by the Makefile before running docker build
# Note: Paths are relative to the repository root since we'll build from there
COPY ${PAYROLL_DIST}/ ./payroll/
COPY ${TOAST_DIST}/ ./toast/
COPY ${PAYLOCITY_DIST}/ ./paylocity/
COPY ${CLIENT_DIST}/ ./client/

# Set environment variables
ENV DOTNET_SYSTEM_GLOBALIZATION_INVARIANT=1

# Default command
CMD ["/bin/bash"]

# Usage examples:
# Build using the Makefile (recommended):
# make upward-container
#
# Or build manually:
# make setup
# (cd Payroll.Tool && make clean linux8)
# (cd Toast.Tool && make clean linux8)
# (cd Paylocity.Tool && make clean linux8)
# (cd Clients/Upward/Upward.Tool && make linux8)
# docker build -t upward-tools Clients/Upward/
#
# Run with volume mounts for logs and mailbox:
# docker run -it --rm \
#   -v /host/path/to/logs:/opt/openarc/Logs \
#   -v /host/path/to/mailbox:/opt/openarc/Mailbox \
#   upward-tools
#
# The host directories should have the subdirectory structure:
# Logs/
#   ├── Paylocity/
#   ├── Toast/
#   └── 7shifts/
# Mailbox/
#   ├── 7shifts/
#   ├── radar/
#   └── cookies/
