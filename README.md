Interoperable payroll synchronization tools including employee and time punch synchronization.

# Getting Started

1. Install podman (from podman.io or brew install)
1. brew install gitversion

## GitHub Setup

1. Create a classic personal action token, set as GITHUB_TOKEN in your environment
1. Set $GITHUB_USERNAME to your username
1. add this to your ~/.bashrc
```echo $GITHUB_TOKEN | podman login ghcr.io -u $GITHUB_USERNAME --password-stdin

## GPG Setup

Optional step, used to sign tags/commits

1. Create a gpg key
1. Set git user.email with -> `git config user.email "<NAME_EMAIL>"
1. Set git user.name with -> `git config user.name "Your Name Here"

# Creating a new release

1. git tag the main branch to create a new major version (when needed)
