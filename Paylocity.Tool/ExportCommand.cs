﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using Payroll.Shared;
using Serilog;
using Paylocity.Tool.Domain;
using System.Security.Cryptography.Xml;
using System.ComponentModel.DataAnnotations;
using DnsClient.Internal;
using System.Text.Encodings.Web;

namespace Paylocity.Tool
{
    class ExportCommand : BaseCommand
    {
        private JsonSerializerOptions ExportSerializerOptions = new JsonSerializerOptions
        {
            WriteIndented = true,
            Encoder = JavaScriptEncoder.UnsafeRelaxedJsonEscaping,
        };

        public void Settings(List<string> args)
        {
            string? atoken = Environment.GetEnvironmentVariable(Config.ENV_KEY_SECRET);
            Console.WriteLine($"export {Config.ENV_KEY_SECRET}={atoken}");
            var companyId = Environment.GetEnvironmentVariable(variable: Config.ENV_KEY_CLIENT_ID);
            Console.WriteLine($"export {Config.ENV_KEY_CLIENT_ID}={companyId}");
        }

        public void Employee(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: Paylocity.Tool.exe export employee <employee id>");
                return;
            }

            try
            {
                using (var service = new PaylocityService())
                {
                    var employee = service.GetEmployee(args[0]).Result;
                    var exportableEmployee = PaylocityService.BuildExportableEmployee(employee);

                    // export as a list, easier to consume by other tools
                    var list = new List<Payroll.Shared.Employee>();
                    list.Add(exportableEmployee);
                    Console.WriteLine(JsonSerializer.Serialize(list, ExportSerializerOptions));
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                if (e.InnerException != null)
                {
                    Log.Logger.Fatal("InnerException: {innerMessage}", e.InnerException.Message);
                }
            }
        }

        public void Employees(List<string> args)
        {
            uint max = uint.MaxValue;
            int delayInMilliseconds = Config.ApiDelayInSeconds() * 1000;

            if (args != null && args.Count > 0)
            {
                max = Convert.ToUInt32(args[0]);
            }

            var employees = new List<Payroll.Shared.Employee>();

            try
            {
                using (var service = new PaylocityService())
                {
                    var empInfoList = service.GetAllEmployeesAsync().Result;
                    uint cnt = 1;

                    foreach (var empId in empInfoList.Keys)
                    {
                        try
                        {
                            var employee = service.GetEmployee(empId).Result;
                            var exportableEmployee = PaylocityService.BuildExportableEmployee(employee);

                            if (string.IsNullOrEmpty(exportableEmployee.ClockSeq))
                            {
                                Log.Logger.Error("Skipping employee {eid} with null ClockSeq, failed to fetch record from Paylocity", empId);
                                continue;
                            }

                            if (!Config.SkipEmployee(exportableEmployee))
                            {
                                employees.Add(exportableEmployee);
                                if (cnt > max) break;
                                cnt++;
                            }
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Error(e.Message);
                            Log.Logger.Error(e.StackTrace);
                        }

                        Thread.Sleep(delayInMilliseconds);
                   }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                if (e.InnerException != null)
                {
                    Log.Logger.Error("InnerException: {innerMessage}", e.InnerException.Message);
                }
            }

            // try to write whatever we got before the exception
            Console.WriteLine(JsonSerializer.Serialize(employees, ExportSerializerOptions));
        }
    }
}
