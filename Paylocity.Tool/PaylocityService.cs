using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using Paylocity.Tool.Domain;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.ComponentModel.Design;
using System.Drawing;
using System.Globalization;
using System.Linq;
using System.Net.Http.Headers;
using System.Security.Policy;
using System.Text;
using System.Threading.Tasks;

namespace Paylocity.Tool
{
    public class PaylocityService : IDisposable
    {
        static private ApiToken _apiToken;
        static readonly Uri _baseAddress;
        private static readonly string CompanyId;
        private static readonly string Host;
        private static readonly int ApiDelayInSeconds = 0; // none for now

        private uint TotalApiRequestsMade { get; set; }
        private DateTime StartTime { get; set; }

        static PaylocityService()
        {
            CompanyId = Config.CompanyId();
            Host = Config.Host();

            _baseAddress = new Uri($"https://{Config.Host()}/");
            _apiToken = new ApiToken();
        }

        public PaylocityService()
        {
            TotalApiRequestsMade = 0;
            StartTime = DateTime.Now;
        }

        private void LogApiRequest()
        {
            TotalApiRequestsMade++;
            if (ApiDelayInSeconds > 0) Thread.Sleep(ApiDelayInSeconds * 1000);
        }

        public static Payroll.Shared.Employee BuildExportableEmployee(Domain.Employee employee)
        {
            var emp = new Payroll.Shared.Employee()
            {
                FirstName = FieldService.GetFirstName(employee),
                LastName = employee.LastName,
                Id = employee.EmployeeId,
                ClockSeq = employee.EmployeeId,
                Active = employee.Status.StatusType == "A",
                StreetAddress = employee.WorkAddress?.Address1,
                CityAddress = employee.WorkAddress?.City,
                State = employee.WorkAddress?.State,
                Zip = employee.WorkAddress?.PostalCode,
                WorkEmail = employee.WorkAddress?.EmailAddress,
                PersonalEmail = employee.HomeAddress?.EmailAddress,
                HireDate = employee.Status.HireDate,
                CellPhone = employee.HomeAddress.Phone,

                PrimaryWorkLocation = FieldService.GetHomeLocation(employee),
                DeptCode = FieldService.GetDeptCode(employee)
            };

            // note the actual date of termination
            if (employee.Status.StatusType == "T")
            {
                emp.TermDate = employee.Status.EffectiveDate;
            }

            // add jobs if there is a primary job
            if (!string.IsNullOrEmpty(employee.DepartmentPosition.CostCenter3))
            {
                var job = new Job()
                {
                    Code = employee.DepartmentPosition.CostCenter3,
                    Rate = Math.Round(Convert.ToDecimal(employee.PrimaryPayRate.BaseRate), 2),
                    Location = employee.DepartmentPosition.CostCenter1,
                    EffectiveDate = employee.PrimaryPayRate.EffectiveDate,
                    IsPrimary = true
                };

                emp.Jobs.Add(job);
            }

            foreach (AdditionalRate rate in employee.AdditionalRate)
            {
                // we have been using rate.Job but are finding CC3 sometimes is populated when job is not
                var jobCode = FieldService.GetJobCode(rate);

                if (string.IsNullOrEmpty(jobCode))
                {
                    Log.Logger.Debug("Assert Failed: Job Code is empty, skipping... emp: {0}, field: {1}",
                        employee.EmployeeId, FieldService.JobCodeField);
                    continue;
                }

                if (rate.Job != rate.CostCenter3 && !string.IsNullOrEmpty(rate.Job))
                {
                    Log.Logger.Warning("Assert Failed: Job Code {0} differs from CostCenter3 {1} (emp: {2})",
                        rate.Job, rate.CostCenter3, employee.EmployeeId);
                }

                var addlJob = new Job()
                {
                    Code = jobCode,
                    Rate = Math.Round(Convert.ToDecimal(rate.Rate), 2),
                    Location = rate.CostCenter1,
                    EffectiveDate = rate.EffectiveDate,
                    IsPrimary = false
                };

                var existingJob = emp.Jobs.FirstOrDefault((f => f.Code == addlJob.Code));

                if (existingJob == null)
                {
                    emp.Jobs.Add(addlJob);
                }
                else
                {
                    if (existingJob.Rate < addlJob.Rate || existingJob.EffectiveDate < addlJob.EffectiveDate)
                    {
                        existingJob.Rate = addlJob.Rate;
                        existingJob.EffectiveDate = addlJob.EffectiveDate;
                    }
                    else
                    {
                        if (existingJob.Rate > addlJob.Rate || existingJob.EffectiveDate > addlJob.EffectiveDate)
                            Log.Logger.Information("Skipping additional rate for job: {0}, rate: {1}, effectiveDate: {2} is less than existing rate: {3}, effectiveDate: {4}",
                            addlJob.Code, addlJob.Rate, addlJob.EffectiveDate, existingJob.Rate, existingJob.EffectiveDate);
                    }
                }
            }

            return emp;
        }

        void IDisposable.Dispose()
        {
            var endTime = DateTime.Now;
            var totalTime = endTime - StartTime;
            var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

            Log.Logger.Debug("Total Api Requests Made = {total}", TotalApiRequestsMade);
            Log.Logger.Debug("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

            if (requestsPerSec > 15)
                Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
            else
                Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
        }

        public Uri EndpointForPath(string path)
        {
            return new Uri($"https://{Host}/{path}");
        }

        private async Task<ApiToken> RequestTokenAsync()
        {
            HttpContent content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("grant_type", "client_credentials"),
                new KeyValuePair<string, string>("scope", "WebLinkAPI")
            });

            try
            {
                using (HttpClient client = new HttpClient())
                {
                    var clientId = Config.ClientId();
                    var clientSecret = Config.Secret();

                    Log.Logger.Debug($"{clientId}:{clientSecret}");
                    client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
                        "Basic", Convert.ToBase64String(System.Text.Encoding.ASCII.GetBytes($"{clientId}:{clientSecret}"))
                    );

                    var endpoint = EndpointForPath("IdentityServer/connect/token");
                    var tokenResponse = await client.PostAsync(endpoint, content);
                    LogApiRequest();
                    if (!tokenResponse.IsSuccessStatusCode)
                    {
                        Log.Logger.Fatal(tokenResponse.ReasonPhrase);
                        Log.Logger.Fatal(await tokenResponse.Content.ReadAsStringAsync());
                        throw new HttpRequestException();
                    }

                    var json = tokenResponse.Content.ReadAsStringAsync().Result;
                    var responseObject = JObject.Parse(json);
                    if (responseObject == null)
                    {
                        throw new InvalidDataException();
                    }
                    else
                    {
                        var token = responseObject["access_token"]?.ToString() ?? string.Empty;
                        var expiresSeconds = (int)(responseObject["expires_in"] ?? 0);
                        var expirationUtc = DateTime.UtcNow.AddSeconds(expiresSeconds);
                        var tokenType = responseObject["token_type"]?.ToString() ?? string.Empty;
                        return new ApiToken
                        {
                            Token = token,
                            ExpirationUtc = expirationUtc,
                            TokenType = tokenType
                        };
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
                throw new ApplicationException();
            }
        }

        public async Task<HttpClient> AuthenicatingHttpClient()
        {
            var client = new HttpClient() { BaseAddress = _baseAddress};

            if (_apiToken == null || _apiToken.IsAboutToExpire)
            {
                _apiToken = await RequestTokenAsync();
            }

            client.DefaultRequestHeaders.Add("Authorization", $"{_apiToken.TokenType} {_apiToken.Token}");
            client.DefaultRequestHeaders.Add("Accept", "application/json");

            Log.Logger.Debug("Auth-Token {tid}", _apiToken.Token);
            return client;
        }

        public async Task<string> RenewCredentials(string code)
        {
            using (var client = await AuthenicatingHttpClient())
            {
                var endpoint = EndpointForPath($"api/v2/credentials/secrets");
                Log.Logger.Debug("Renewing credentials via {url}", endpoint);

                var content = new StringContent($"{{\"code\":\"{code}\"}}", Encoding.UTF8, "application/json");
                var response = await client.PostAsync(endpoint, content);
                LogApiRequest();

                var responseBody = await response.Content.ReadAsStringAsync();
                Log.Logger.Debug(responseBody);

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("Failed to renew credentials: {code}/{msg}", response.StatusCode, response.ReasonPhrase);
                    Log.Logger.Error(responseBody);
                    return string.Empty;
                }

                return responseBody;
            }
        }

        public async Task<Domain.Employee> GetEmployee(string employeeId)
        {
            using (var client = await AuthenicatingHttpClient())
            {
                const int maxRetries = 3;
                const int retryDelayMilliseconds = 1000;

                var endpoint = EndpointForPath($"api/v2/companies/{CompanyId}/employees/{employeeId}");

                for (int attempt = 1; attempt <= maxRetries; attempt++)
                {
                    try
                    {
                        Log.Logger.Debug("Fetching employee info via {url} (Attempt {attempt}/{maxRetries})", endpoint, attempt, maxRetries);

                        var response = await client.GetAsync(endpoint);
                        LogApiRequest();

                        var responseBody = await response.Content.ReadAsStringAsync();
                        Log.Logger.Debug(responseBody);

                        if (response.IsSuccessStatusCode)
                        {
                            return JsonConvert.DeserializeObject<Domain.Employee>(responseBody);
                        }

                        Log.Logger.Error("Failed to fetch employee {eid}: {code}/{msg} (Attempt {attempt}/{maxRetries})", employeeId, response.StatusCode, response.ReasonPhrase, attempt, maxRetries);
                        Log.Logger.Error(responseBody);
                    }
                    catch (Exception e)
                    {
                        Log.Logger.Error("Exception occurred while fetching employee {eid} (Attempt {attempt}/{maxRetries}): {msg}", employeeId, attempt, maxRetries, e.Message);
                        Log.Logger.Error(e.StackTrace);
                        if (e.InnerException != null)
                        {
                            Log.Logger.Error("InnerException: {innerMessage}", e.InnerException.Message);
                        }

                        if (attempt == maxRetries)
                        {
                            Log.Logger.Fatal("All retry attempts exhausted for employee {eid}: {msg}");
                        }
                    }

                    if (attempt < maxRetries)
                    {
                        Log.Logger.Warning("Retrying in {delay} milliseconds...", retryDelayMilliseconds);
                        Thread.Sleep(retryDelayMilliseconds);
                    }
                }

                return new Domain.Employee();
            }
        }

        public async Task<SortedDictionary<string, EmployeeListInfo>> GetAllEmployeesAsync()
        {
            using (var client = await AuthenicatingHttpClient())
            {
                var pageNumber = -1;
                var employeesById = new SortedDictionary<string, EmployeeListInfo>();

                //get the first page of results
                var countRetrieved = 0;
                int totalEmployees = 0;

                do
                {
                    pageNumber++;
                    var url = $"api/v2/companies/{CompanyId}/employees?pagesize=1000&pagenumber={pageNumber}&includetotalcount=true";
                    Log.Logger.Debug("Fetching employees via {url}", url);

                    var response = await client.GetAsync(url);
                    LogApiRequest();

                    if (pageNumber == 0)
                    {
                        var headerValue = response.Headers.GetValues("X-Pcty-Total-Count").FirstOrDefault();
                        Log.Logger.Debug("headerValue={0}", headerValue);

                        if (!string.IsNullOrEmpty(headerValue))
                        {
                            totalEmployees = int.Parse(headerValue);
                        }
                    }

                    var pageOfEmployees = JsonConvert.DeserializeObject<List<EmployeeListInfo>>(await response.Content.ReadAsStringAsync()) ?? new List<EmployeeListInfo>();
                    countRetrieved += pageOfEmployees.Count();

                    foreach (var e in pageOfEmployees)
                    {
                        if (employeesById.TryGetValue(e.EmployeeId, out var existingEmployee))
                        {
                            if (existingEmployee.StatusCode != e.StatusCode)
                                Log.Logger.Error("Duplicate employee found: {eid}, with different status codes: {x} => {x}", e.EmployeeId, existingEmployee.StatusCode, e.StatusCode);
                            continue;
                        }

                        employeesById.Add(e.EmployeeId, e);
                    }

                } while (countRetrieved < totalEmployees);

                return employeesById;
            }
        }
    }
}
