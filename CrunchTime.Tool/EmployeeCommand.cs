﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;
using Payroll.Shared;
using Serilog;

namespace CrunchTime.Tool
{
    public class EmployeeCommand
    {
        public void Audit(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine($"Usage: CrunchTime.Tool.exe employee audit <prefix>");
                return;
            }

            var prefix = args[0].Trim();
            bool includeTerminated = false;

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var activeLocations = crunchTimeService.GetActiveLocationsAsync().Result;

                foreach (var location in activeLocations)
                {
                    try
                    {
                        var employeeNumbers = includeTerminated
                            ? crunchTimeService.GetEmployeeNumbersAsync(location.Code, includeTerminated: true).Result
                            : crunchTimeService.GetActiveEmployeeNumbersAsync(location.Code).Result;

                        var employees = employeeNumbers
                            .Select(number => crunchTimeService.GetEmployeeDetailsAsync(number).Result)
                            .OrderBy(e => e.LastName)
                            .ThenBy(e => e.FirstName)
                            .ToList();

                        var uniqueEmpIds = new Dictionary<string, string>();
                        foreach (var e in employees)
                        {
                            if (!uniqueEmpIds.ContainsKey(e.EmployeeNumber))
                            {
                                uniqueEmpIds.Add(e.EmployeeNumber, $"{e.LastName}, {e.FirstName}");
                                continue;
                            }

                            var existingEmpName = uniqueEmpIds[e.EmployeeNumber];
                            if (existingEmpName == $"{e.LastName}, {e.FirstName}")
                            {
                                Log.Logger.Warning($"Duplicate employee number: {e.EmployeeNumber}, but matching names: {existingEmpName} and {e.FirstName} {e.LastName}, so skipping");
                                continue;
                            }

                            Log.Logger.Error($"Duplicate employee number: {e.EmployeeNumber}, but different names: {existingEmpName} and {e.FirstName} {e.LastName}");

                            if (e.EmployeeNumber.StartsWith(prefix))
                            {
                                var fixedId = e.EmployeeNumber.Replace(prefix, "");
                                if (uniqueEmpIds.ContainsKey(fixedId))
                                {
                                    Log.Logger.Error($"Duplicate FIXED employee number: {fixedId}, but different names: {uniqueEmpIds[fixedId]} and {e.FirstName} {e.LastName}");
                                }
                                else
                                {
                                    uniqueEmpIds.Add(fixedId, $"{e.LastName}, {e.FirstName}");
                                }
                            }
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Logger.Error(e, $"Error listing employees for location {location.Code}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
            }
        }

        public void List(List<string> args)
        {
            ListEmployees(args, false);
        }

        public void Terms(List<string> args)
        {
            ListEmployees(args, true);
        }

        private void ListEmployees(List<string> args, bool includeTerminated)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine($"Usage: CrunchTime.Tool.exe employee {(includeTerminated ? "terms" : "list")} <location code>");
                return;
            }

            var locationCode = args[0];

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                var activeLocations = crunchTimeService.GetActiveLocationsAsync().Result;
                if (locationCode != "all") activeLocations.RemoveAll(x => x.Code != locationCode);

                foreach (var location in activeLocations)
                {
                    try
                    {
                        var employeeNumbers = includeTerminated
                            ? crunchTimeService.GetEmployeeNumbersAsync(location.Code, includeTerminated: true).Result
                            : crunchTimeService.GetActiveEmployeeNumbersAsync(location.Code).Result;

                        var employees = employeeNumbers
                            .Select(number => crunchTimeService.GetEmployeeDetailsAsync(number).Result)
                            .Where(e => e != null)
                            .OrderBy(e => e.LastName ?? "")
                            .ThenBy(e => e.FirstName ?? "")
                            .ToList();

                        foreach (CrunchTimeEmployee e in employees)
                        {
                            string fullName = $"{e.LastName}, {e.FirstName}".PadRight(30);
                            int positionCount = e.EmployeePositions?.Count ?? 0;
                            int locationCount = e.EmployeeLocations?.Count ?? 0;
                            var status = e.Status == "Terminated" ? e.TerminationReason : e.Status;
                            Console.WriteLine($"{e.EmployeeNumber.PadRight(10)}\t{fullName}\t{e.PayrollIdNumber?.PadRight(10)}\tPositions: {positionCount}\tLocations: {locationCount}\t{location?.Code}\t{location?.Name}\t{status}");
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Logger.Error(e, $"Error listing employees for location {location.Code}");
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
            }
        }

        public void Raw(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe employee raw <employee number>");
                Console.WriteLine("  note: this command does not use the employee cache");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                CrunchTimeEmployee employee = crunchTimeService.GetEmployeeDetailsAsync(args[0], false).Result;
                if (employee == null)
                {
                    Log.Logger.Error($"Employee {args[0]} not found");
                    return;
                }

                ConsoleService.PrintFormattedJson(employee);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error getting employee details");
            }
        }

        public void View(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe employee view <employee number>");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                CrunchTimeEmployee employee = crunchTimeService.GetEmployeeDetailsAsync(args[0]).Result;
                if (employee == null)
                {
                    Log.Logger.Error($"Employee {args[0]} not found");
                    return;
                }

                Log.Logger.Debug(JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true }));
                var payrollEmployee = Converter.ConvertEmployee(employee);
                ConsoleService.PrintFormattedJson(payrollEmployee);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error getting employee details");
            }
        }

        public void Reactivate(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe employee reactivate <employee number>");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                CrunchTimeEmployee fullStoreEmployeeRecord = crunchTimeService.GetEmployeeDetailsAsync(args[0], false).Result;

                if (fullStoreEmployeeRecord.Status == "Active")
                {
                    Console.WriteLine($"Employee {fullStoreEmployeeRecord.EmployeeNumber} is already active.");
                    return;
                }

                fullStoreEmployeeRecord.Status = "Active";
                fullStoreEmployeeRecord.StatusCode = "Active";
                var result = crunchTimeService.SaveEmployeeAsync(fullStoreEmployeeRecord).Result;

                Console.WriteLine($"Employee {fullStoreEmployeeRecord.EmployeeNumber} has been reactivated.");
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error reactivating employee");
            }
        }

        /// <summary>
        /// This is a test method for demonstration purposes.
        /// </summary>
        public void Save(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe employee save <employee number>");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                CrunchTimeEmployee fullStoreEmployeeRecord = crunchTimeService.GetEmployeeDetailsAsync(args[0], false).Result;
                Log.Logger.Debug(JsonSerializer.Serialize(fullStoreEmployeeRecord, new JsonSerializerOptions { WriteIndented = true }));

                fullStoreEmployeeRecord.EmailAddress = "<EMAIL>";
                var result = crunchTimeService.SaveEmployeeAsync(fullStoreEmployeeRecord).Result;
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error saving employee");
            }
        }

        /// <summary>
        /// This is a test method for demonstration purposes.
        /// </summary>
        public void Add(List<string> args)
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe employee add <employee number>");
                return;
            }

            try
            {
                using var crunchTimeService = new CrunchTimeService();
                CrunchTimeEmployee employee = new CrunchTimeEmployee();

                employee.EmployeeNumber = "0005";
                employee.FirstName = "Test 5";
                employee.LastName = "Test 5";
                employee.PayrollIdNumber = "TST05";
                employee.EmailAddress = "<EMAIL>";
                employee.Status = "Active";
                employee.DateHired = DateTime.Now.ToString("MM/dd/yyyy");
                employee.DateOfBirth = DateTime.Now.ToString("MM/dd/yyyy");
                employee.EmployeeLocations = new List<EmployeeLocation>();
                employee.EmployeeLocations.Add(new EmployeeLocation {
                    LocationCode = "10026",
                    PrimaryLocationFlag = true
                });
                employee.EmployeePositions = new List<EmployeePosition>();
                employee.EmployeePositions.Add(new EmployeePosition {
                    Id=5,
                    AltLocationsFlag = "Y",
                    PositionCode = "10018",
                    PosCode = "10018", //TODO ask client what we should use for POS code
                    PrimaryPositionFlag = "Y",
                    PayRate = 12
                });
                employee.PayType = "H"; //Todo - ask client what we should use for pay type
                var result = crunchTimeService.SaveEmployeeAsync(employee).Result;
            }
            catch (Exception e)
            {
                Log.Logger.Error(e, "Error getting employee details");
            }
        }
    }
}
