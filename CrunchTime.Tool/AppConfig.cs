﻿using Payroll.Shared;
using System;
using System.Linq;
using System.Collections.Generic;

namespace CrunchTime.Tool
{
    public class AppConfig : PosConfig
    {
        public static readonly string APP_SECTION = "crunchtime";
        public static readonly string POS_EMPLOYEES_CACHE_COLLECTION = "pos_employees";
        public static readonly string POS_LOCATIONS_CACHE_COLLECTION = "pos_locations";

        public static readonly string ENV_KEY_USER_API_KEY = "CTIME_USER_API_KEY";
        public static readonly string ENV_KEY_EMP_API_KEY = "CTIME_EMP_API_KEY";
        public static readonly string ENV_KEY_CLOCK_API_KEY = "CTIME_CLOCK_API_KEY";
        public static readonly string ENV_KEY_LOCATION_API_KEY = "CTIME_LOCATION_API_KEY";
        public static readonly string ENV_KEY_TIMEZONE_API_KEY = "PTOOLS_TIMEZONE_API_KEY";

        public static readonly string ENV_KEY_USERNAME = "CTIME_USERNAME";
        public static readonly string ENV_KEY_PASSWORD = "CTIME_PASSWORD";
        public static readonly string ENV_KEY_SITENAME = "CTIME_SITENAME";

        public static readonly string SkipLocationSection = "crunchtime_skip_locations";

        public HashSet<string> LocationsToSkip { get; private set; } = new HashSet<string>();

        public string Endpoint { get; private set; }
        public string Franchise { get; private set; } = "*";
        public string DefaultTimeZone { get; private set; } = "America/Chicago";
        public string StdTerminationReason { get; private set; } = ""; // if not set, will use "employee.Description" field
        public bool ModifyPayrollIds { get; private set; } = false;

        public readonly int NewHireDaysWindow = 3;

        public bool StrictMode { get; private set; } = true;

        public AppConfig() : base(APP_SECTION)
        {
            Setting.Init();
            Endpoint = Setting.Get(SettingSection, "endpoint");
            Franchise = Setting.Get(SettingSection, "franchise");
            StrictMode = Setting.GetBoolean(SettingSection, "strict_mode");
            DefaultTimeZone = Setting.Get(SettingSection, "timezone_default");
            ModifyPayrollIds = Setting.GetBoolean(SettingSection, "modify_payroll_ids");

            StdTerminationReason = Setting.Get(SettingSection, "std_term_reason");
            {
                var skiplist = Setting.ListSection(SkipLocationSection);
                LocationsToSkip = skiplist.Keys.ToHashSet();
            }

            {
                string limit = Setting.Get(SettingSection, "new_hire_days_window");
                if (!string.IsNullOrEmpty(limit))
                {
                    NewHireDaysWindow = int.Parse(limit);
                }
            }
        }

        public string UserApiKey()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_USER_API_KEY);
        }

        public string EmpApiKey()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_EMP_API_KEY);
        }

        public string LocationApiKey()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_LOCATION_API_KEY);
        }

        public string ClockApiKey()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_CLOCK_API_KEY);
        }

        public string Username()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_USERNAME);
        }

        public string Password()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_PASSWORD);
        }

        public string Sitename()
        {
            return Environment.GetEnvironmentVariable(ENV_KEY_SITENAME);
        }

        public bool SkipLocation(string location)
        {
            if (string.IsNullOrEmpty(location)) return false;

            return LocationsToSkip.Contains(location);
        }

        public static AppConfig Instance { get; private set; } = new AppConfig();
    }
}
