﻿using System;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using System.Text.Json;
using System.Linq;

namespace CrunchTime.Tool
{
    public class ImportCommand
    {
        private Dictionary<string, Payroll.Shared.Location> LocationMap = new Dictionary<string,Payroll.Shared.Location>();

        public void Changes(List<string> args)
        {
            var appConfig = new AppConfig();

            try
            {
                if (!ConsoleService.TryGetChangesFromInput(out var changes))
                {
                    Log.Logger.Error("Failed to parse changes list");
                    return;
                }

                if (changes == null)
                {
                    Log.Logger.Error("Failed to load changes list");
                    return;
                }


                using (var service = new CrunchTimeService())
                {
                    foreach (var change in changes)
                    {
                        //service.PatchEmployeeAsync(change.PrimaryKey, change.PropertyName, change.NewValue).Wait();
                        Log.Logger.Information("Would update {property} from '{old}' to '{new}' for {eid}",
                            change.PropertyName, change.OldValue, change.NewValue, change.PrimaryKey);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        private void ProcessImportCommand(ExecutionMode executionMode, List<string> args, bool needTerminated, string syncLabel, int syncLimit,
            Func<CrunchTimeService, string, IDictionary<string, CrunchTimeEmployee>, ExecutionMode, Payroll.Shared.Employee, bool> func)
        {
            try
            {
                if (!ConsoleService.TryGetEmployeesFromInput(out var employeesToImport))
                {
                    Log.Logger.Error("Failed to parse employee list");
                    return;
                }

                if (employeesToImport == null)
                {
                    Log.Logger.Error("Failed to load employee list");
                    return;
                }

                if (args == null || args.Count < 1)
                {
                    Console.WriteLine("Usage: CrunchTime.Tool.exe import [hires|terms|sync] <store-number|all>");
                    return;
                }

                string unitId = args[0];
                var includeTerminated = Environment.GetEnvironmentVariable("INCLUDE_TERMINATED") == "1";

                if (executionMode == ExecutionMode.Execute)
                {
                    // both the config file and the command line must confirm execution
                    if (args.Count < 2) executionMode = ExecutionMode.DryRun;
                    else if (args[1] != "doit") executionMode = ExecutionMode.DryRun;
                }

                Log.Logger.Information("CrunchTime.Tool Mode={mode}, Target={loc}, Op={op}, Limit={lmt}", executionMode, unitId, syncLabel, syncLimit);

                using (var service = new CrunchTimeService())
                {
                    var locations = service.GetActiveLocationsAsync().Result;
                    if (unitId != "all") locations.RemoveAll(x => x.Code != unitId);
                    LocationMap = locations.ToDictionary(x => x.Code);

                    var employeeDirectory = new Dictionary<string, Dictionary<string, CrunchTimeEmployee>>();
                    var cacheOptions = new CacheableFetchOptions() { IncludeTerminated = includeTerminated };

                    foreach (var location in locations)
                    {
                        var employees = service.GetCrunchTimeEmployeesAsync(location.Code, cacheOptions).Result;
                        employeeDirectory.Add(location.Code, employees);
                    }

                    var syncCnt = 0;

                    foreach (var employee in employeesToImport)
                    {
                        if (unitId != "all" && employee.PrimaryWorkLocation != unitId)
                            continue;

                        if (!employeeDirectory.TryGetValue(employee.PrimaryWorkLocation, out var directoryForLocation))
                        {
                            Log.Logger.Error("  Failed to locate employee directory for {rid}, skipping {fname} {lname} - {cseq}", employee.PrimaryWorkLocation, employee.FirstName, employee.LastName, employee.Id);
                            continue;
                        }

                        var processed = func(service, employee.PrimaryWorkLocation, directoryForLocation, executionMode, employee);
                        if (processed) syncCnt++;

                        if (syncLimit > 0 && syncCnt >= syncLimit)
                        {
                            Log.Logger.Warning("  {lbl} limit {x} reached, exiting...", syncLabel, syncLimit);
                            return;
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
            }
        }

        private bool UpdateEmployeeProperty<T>(CrunchTimeEmployee employee, Func<T> getter, Action<T> setter, T newValue, string propertyName, Payroll.Shared.Employee sourceEmployee)
        {
            // Check if newValue is null (or default for value types)
            if (EqualityComparer<T>.Default.Equals(newValue, default(T)))
            {
                return false;
            }

            // Special case for string type to check for empty strings
            if (typeof(T) == typeof(string) && string.IsNullOrEmpty(newValue as string))
            {
                return false;
            }

            T currentValue = getter();
            if (!EqualityComparer<T>.Default.Equals(currentValue, newValue))
            {
                Log.Logger.Information("  Updating {property} from '{old}' to '{new}' for {eid}/{cseq}",
                    propertyName, currentValue, newValue, sourceEmployee.Id, sourceEmployee.ClockSeq);

                setter(newValue);
                return true;
            }

            return false;
        }


        private bool UpdateEmployeePositionProperty<T>(EmployeePosition employeePosition, Func<T> getter, Action<T> setter, T newValue, string propertyName, Payroll.Shared.Employee sourceEmployee)
        {
            // Check if newValue is null (or default for value types)
            if (EqualityComparer<T>.Default.Equals(newValue, default(T)))
            {
                return false;
            }

            // Special case for string type to check for empty strings
            if (typeof(T) == typeof(string) && string.IsNullOrEmpty(newValue as string))
            {
                return false;
            }

            T currentValue = getter();
            if (!EqualityComparer<T>.Default.Equals(currentValue, newValue))
            {
                Log.Logger.Information("  Updating {property} from '{old}' to '{new}' for {eid}/{cseq} for job {job}",
                    propertyName, currentValue, newValue, sourceEmployee.Id, sourceEmployee.ClockSeq, employeePosition.PositionCode);

                setter(newValue);
                return true;
            }

            return false;
        }

        private bool UpdateEmployeePropertyIgnoreCase(CrunchTimeEmployee employee, Func<string> getter, Action<string> setter, string newValue, string propertyName, Payroll.Shared.Employee sourceEmployee)
        {
            var currentValue = getter();
            if (currentValue.Equals(newValue, StringComparison.OrdinalIgnoreCase))
            {
                return false;
            }

            return UpdateEmployeeProperty(employee, getter, setter, newValue, propertyName, sourceEmployee);
        }

        private bool UpdateEmployeePrimaryLocation(CrunchTimeEmployee fullStoreEmployeeRecord, Payroll.Shared.Employee employee)
        {
            bool employeeUpdate = false;
            bool foundPrimaryLocation = false;

            if (fullStoreEmployeeRecord.EmployeeLocations != null)
            {
                foreach (var location in fullStoreEmployeeRecord.EmployeeLocations)
                {
                    // Check primary location
                    if (location.LocationCode == employee.PrimaryWorkLocation)
                    {
                        foundPrimaryLocation = true;
                        if (location.PrimaryLocationFlag != true)
                        {
                            location.PrimaryLocationFlag = true;
                            employeeUpdate = true;
                        }
                        else
                        {
                            Log.Logger.Debug("    Employee {cseq} already has primary location {loc}", employee.ClockSeq, employee.PrimaryWorkLocation);
                        }
                    }
                    else
                    {
                        // Check other locations marked as primary
                        if (location.PrimaryLocationFlag == true)
                        {
                            Log.Logger.Information("    Employee {cseq} is primary at another location, disabling primary location flag for {loc}",
                                employee.ClockSeq, location.LocationCode);
                            location.PrimaryLocationFlag = false;
                            employeeUpdate = true;
                        }
                    }
                }
            }

            if (!foundPrimaryLocation)
            {
                var primaryWorkLocation = new EmployeeLocation
                {
                    LocationCode = employee.PrimaryWorkLocation,
                    EmployeeNumber = fullStoreEmployeeRecord.EmployeeNumber,
                    PosId = fullStoreEmployeeRecord.EmployeeNumber,
                    PosExportFlag = true,
                    PrimaryLocationFlag = true,
                    DeleteFlag = "N",
                    AllocateLaborTo = "N",
                };

                if (LocationMap.ContainsKey(employee.PrimaryWorkLocation))
                {
                    var location = LocationMap[employee.PrimaryWorkLocation];
                    primaryWorkLocation.LocationName = location.Name;
                    primaryWorkLocation.LocationStateName = location.State;
                }
                else Log.Logger.Warning("  Failed to find location details for {loc} for {cseq}...", employee.PrimaryWorkLocation, employee.ClockSeq);

                if (fullStoreEmployeeRecord.EmployeeLocations == null)
                        fullStoreEmployeeRecord.EmployeeLocations = new List<EmployeeLocation>();

                fullStoreEmployeeRecord.EmployeeLocations.Add(primaryWorkLocation);
                employeeUpdate = true;
            }

            return employeeUpdate;
        }

        public void Hires(List<string> args)
        {
            var results = new List<Result>();
            var appConfig = new AppConfig();

            var newHireDaysWindow = DateTime.Now.Date.AddDays(-appConfig.NewHireDaysWindow);
            var forceSync = Environment.GetEnvironmentVariable("FORCE_SYNC") == "1";

            ProcessImportCommand(appConfig.HireMode, args, false, "Hire", appConfig.HireLimit,
             (CrunchTimeService service, string restaurant, IDictionary<string, CrunchTimeEmployee> employeeDirectory,
                ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                if (!employee.Active)
                {
                    Log.Logger.Warning("  Skipping creation of inactive employee {cseq}", employee.ClockSeq);
                    return false;
                }

                if (employee.HireDate == null)
                {
                    Log.Logger.Warning("  Skipping creation of employee with no hire date {cseq}", employee.ClockSeq);
                    return false;
                }

                if (employee.HireDate == null)
                {
                    Log.Logger.Warning("  Skipping creation of employee with no hire date {cseq}", employee.ClockSeq);
                    return false;
                }

                if (employee.HireDate < newHireDaysWindow && (employee.EmployeeAdded == null || employee.EmployeeAdded < newHireDaysWindow))
                {
                    var last30days = DateTime.Now.AddDays(-30);
                    if (employee.HireDate > last30days || employee.EmployeeAdded > last30days)
                    {
                        Log.Logger.Warning("  Hire date {h} and employee added date {a} older than {days} days for {fname} {lname} - empno:{id}, pid:{cseq} at location {loc}, skipping creation...",
                            employee.HireDate?.ToShortDateString(),
                            employee.EmployeeAdded?.ToShortDateString(),
                            appConfig.NewHireDaysWindow,
                            employee.FirstName,
                            employee.LastName,
                            employee.Id,
                            employee.ClockSeq,
                            restaurant);
                    }
                    return false;
                }

                var result = new Result();
                result.AddArg("pkey", employee.Id);
                result.AddArg("date", employee.HireDate?.ToShortDateString());
                result.AddArg(key: "name", $"{employee.FirstName} {employee.LastName}");
                result.AddArg("restaurant", val: restaurant);

                CrunchTimeEmployee? fullStoreEmployeeRecord = service.GetEmployeeDetailsAsync(employee.ClockSeq, false).Result;

                //Log.Logger.Information("employeeJson Details: {employeeJson}", JsonSerializer.Serialize(employee, new JsonSerializerOptions { WriteIndented = true }));
                if (fullStoreEmployeeRecord != null)
                {
                    //Log.Logger.Information("dcEmployeeJson Details: {dcEmployeeJson}", JsonSerializer.Serialize(dcEmployee, new JsonSerializerOptions { WriteIndented = true }));
                    // if already active, no need to do anything
                    if (fullStoreEmployeeRecord.Status == "Active" && !forceSync)
                    {
                        Log.Logger.Debug("    Employee is already active, skipping rehire for {fname} {lname} - empno:{id}, pid:{cseq} at location {loc}, hired: {h}",
                            employee.FirstName, employee.LastName, employee.Id, employee.ClockSeq, restaurant, employee.HireDate?.ToShortDateString());
                        return false;
                    }

                    Log.Logger.Information("    Re-enabling an existing employee account {fname} {lname} - empno:{id}, pid:{cseq} at location {loc}, hired: {h}",
                        employee.FirstName, employee.LastName, employee.Id, employee.ClockSeq, restaurant, employee.HireDate?.ToShortDateString());

                    if (fullStoreEmployeeRecord == null)
                    {
                        Log.Logger.Error("Failed to get employee details from CrunchTime for employee {id}/", employee.ClockSeq);
                        return false;
                    }

                    if (executionMode == ExecutionMode.Execute)
                    {
                        //TODO: reactivate employee
                        fullStoreEmployeeRecord.Status = "Active";
                        fullStoreEmployeeRecord.StatusCode = "Active";

                        UpdateCrunchTimeEmployeeRecord(employee, fullStoreEmployeeRecord);
                        service.SaveEmployeeAsync(fullStoreEmployeeRecord).Wait();
                    }
                    else
                    {
                        Log.Logger.Information("    Would have reactivated employee {cseq}", employee.ClockSeq);
                    }

                    result.ResultType = ResultType.ReHire;
                    results.Add(result);

                    return true;
                }

                Log.Logger.Information("    Creating new employee {fname} {lname} - {id}/{cseq} at location {loc}, hired: {h}",
                    employee.FirstName, employee.LastName, employee.Id, employee.ClockSeq, restaurant, employee.HireDate?.ToShortDateString());

                if (executionMode == ExecutionMode.Execute) {
                    // Check if employee already exists in CrunchTime

                    if (fullStoreEmployeeRecord != null) {
                        Log.Logger.Warning("    Employee {cseq} already exists in CrunchTime, skipping creation", employee.ClockSeq);
                        return false;
                    }

                    var newEmployee = Converter.ConvertToCrunchTimeEmployee(employee);
                    var saveResult = service.SaveEmployeeAsync(newEmployee).Result;
                } else {
                    Log.Logger.Information("    Would have added employee {id}/{cseq}", employee.Id, employee.ClockSeq);
                }

                return true;
            });
        }

        private bool UpdateCrunchTimeEmployeeRecord(Employee fromEmployee, CrunchTimeEmployee toEmployee)
        {
            var employeeUpdate = false;

            employeeUpdate |= UpdateEmployeePropertyIgnoreCase(toEmployee,
                () => toEmployee.FirstName,
                (value) => toEmployee.FirstName = value,
                fromEmployee.FirstName, "first name", fromEmployee);

            employeeUpdate |= UpdateEmployeePropertyIgnoreCase(toEmployee,
                () => toEmployee.LastName,
                (value) => toEmployee.LastName = value,
                fromEmployee.LastName, "last name", fromEmployee);

            employeeUpdate |= UpdateEmployeePropertyIgnoreCase(toEmployee,
                () => toEmployee.EmailAddress,
                (value) => toEmployee.EmailAddress = value,
                fromEmployee.PersonalEmail, "email", fromEmployee);

            employeeUpdate |= UpdateEmployeeProperty(toEmployee,
                () => toEmployee.PhoneNumber,
                (value) => toEmployee.PhoneNumber = value,
                fromEmployee.OfficePhone, "phone", fromEmployee);

            employeeUpdate |= UpdateEmployeeProperty(toEmployee,
                () => toEmployee.DateOfBirth,
                (value) => toEmployee.DateOfBirth = value,
                fromEmployee.DateOfBirth?.ToString("MM/dd/yyyy"), "birthday", fromEmployee);

            if (fromEmployee.RehireDate.HasValue && fromEmployee.HireDate.HasValue && fromEmployee.RehireDate > fromEmployee.HireDate)
            {
                employeeUpdate |= UpdateEmployeeProperty(toEmployee,
                () => toEmployee.DateHired,
                (value) => toEmployee.DateHired = value,
                fromEmployee.RehireDate?.ToString("MM/dd/yyyy"), "rehire date", fromEmployee);
            }

            employeeUpdate |= UpdateEmployeePrimaryLocation(toEmployee, fromEmployee);

            return employeeUpdate;
        }

        public void Sync(List<string> args)
        {
            var results = new List<Result>();
            var appConfig = new AppConfig();
            var includeTerminated = Environment.GetEnvironmentVariable("INCLUDE_TERMINATED") == "1";

            ProcessImportCommand(appConfig.SyncMode, args, false, "Sync", appConfig.SyncLimit,
             (CrunchTimeService service, string locationId, IDictionary<string, CrunchTimeEmployee> employeeDirectory, ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                // update toast record as needed
                bool employeeUpdate = false;
                bool jobUpdate = false;
                int currentJobCount = -1;

                Dictionary<string, string> attrs = employee.Attributes;
                string nickName = attrs?.GetValueOrDefault(key: "nickname") ?? "";
                string jobTitle = attrs?.GetValueOrDefault(key: "business_title") ?? "";
                string terminationReason = attrs?.GetValueOrDefault(key: "termination_reason") ?? "";

                CrunchTimeEmployee fullStoreEmployeeRecord = null;
                {
                    // The main purpose of this call is to see if the employee record is in the scope of locations that we are supposed to be processing.
                    if (!employeeDirectory.TryGetValue(employee.ClockSeq, out CrunchTimeEmployee ctEmployee))
                    {
                        Log.Logger.Information("Skipping sync of a non-existent employee or terminated employee {id}/{cseq} - {fn} {ln} @ loc {loc}",
                            employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                        return false;
                    }

                    if (!employee.Active && !includeTerminated)
                    {
                        Log.Logger.Warning("Skipping sync of inactive employee {id}/{cseq}", employee.Id, employee.ClockSeq);
                        return false;
                    }

                    fullStoreEmployeeRecord = service.GetEmployeeDetailsAsync(ctEmployee.EmployeeNumber, false).Result;
                    if (fullStoreEmployeeRecord == null)
                    {
                        Log.Logger.Error("Failed to get employee details from CrunchTime for employee {id}/", ctEmployee.EmployeeNumber);
                        return false;
                    }

                    Log.Logger.Information("Processing employee {0}/{1} - {2}, location: {3} ({4} jobs)", fullStoreEmployeeRecord.EmployeeNumber,
                        fullStoreEmployeeRecord.PayrollIdNumber, fullStoreEmployeeRecord.LastName,
                        fullStoreEmployeeRecord.PrimaryLocationCode, fullStoreEmployeeRecord.EmployeePositions?.Count);

                    if (fullStoreEmployeeRecord.EmployeePositions != null)
                    {
                        currentJobCount = fullStoreEmployeeRecord.EmployeePositions.Count;
                    }

                    //Log.Logger.Debug("Full Store Employee Record: {fullStoreEmployeeRecord}", JsonSerializer.Serialize(fullStoreEmployeeRecord, new JsonSerializerOptions { WriteIndented = true }));
                }
                Log.Logger.Debug("Employee details: {employee}", JsonSerializer.Serialize(fullStoreEmployeeRecord, new JsonSerializerOptions { WriteIndented = true }));

                employeeUpdate |= UpdateCrunchTimeEmployeeRecord(employee, fullStoreEmployeeRecord);

                employeeUpdate |= UpdateEmployeeProperty(fullStoreEmployeeRecord,
                () => fullStoreEmployeeRecord.TerminationReason,
                (value) => fullStoreEmployeeRecord.TerminationReason = value,
                terminationReason, "termination reason", employee);

                if (appConfig.ModifyPayrollIds)
                {
                    var newPayrollId = FieldService.GetPayrollId(employee);
                    if (newPayrollId != fullStoreEmployeeRecord.PayrollIdNumber)
                    {
                        Log.Logger.Debug("    Updating Payroll Id from '{old}' to '{new}' for {eid}/{cseq}",
                            fullStoreEmployeeRecord.PayrollIdNumber, newPayrollId, employee.Id, employee.ClockSeq);
                        fullStoreEmployeeRecord.PayrollIdNumber = newPayrollId;
                        employeeUpdate = true;
                    }
                }

                var processTracker = employee.Jobs.ToDictionary(x => x, x => false);
                foreach (var employeePosition in fullStoreEmployeeRecord.EmployeePositions)
                {
                    bool foundJob = false;
                    foreach (var job in employee.Jobs)
                    {
                        if (employeePosition.PositionCode != job.Code) continue;

                        foundJob = true;
                        processTracker[job] = true;
                        Log.Logger.Debug("Reviewing existing job for employee {cseq}: {jobName}/{jobCode} with rate {rate}.",
                            employee.ClockSeq, employeePosition.PositionName, employeePosition.PositionCode, employeePosition.PayRate);

                        jobUpdate |= UpdateEmployeePositionProperty(employeePosition,
                            () => employeePosition.PayRate,
                            (value) => employeePosition.PayRate = value,
                            job.Rate, "pay rate", employee);

                        jobUpdate |= UpdateEmployeePositionProperty(employeePosition,
                            () => employeePosition.PrimaryPositionFlag,
                            (value) => employeePosition.PrimaryPositionFlag = value,
                            job.IsPrimary ? "Y" : "N", "primary position flag", employee);
                    }

                    if (!foundJob && employeePosition.PrimaryPositionFlag == "Y")
                    {
                        Log.Logger.Information("  Marking job {jobName}/{jobCode} as non-primary for employee {cseq} with rate {rate}.",
                            employeePosition.PositionName, employeePosition.PositionCode, employee.ClockSeq, employeePosition.PayRate);
                        employeePosition.PrimaryPositionFlag = "N";
                        jobUpdate = true;
                    }
                }

                foreach (var entry in processTracker)
                {
                    if (!entry.Value)
                    {
                        var job = entry.Key;
                        Log.Logger.Information("  Adding job {jobName}/{jobCode} for employee {cseq} with rate {rate}.", job.Name, job.Code, employee.ClockSeq, job.Rate);
                        fullStoreEmployeeRecord.EmployeePositions.Add(new EmployeePosition
                        {
                            PosCode = job.Code,
                            PositionCode = job.Code,
                            PrimaryPositionFlag = job.IsPrimary ? "Y" : "N",
                            PayRate = job.Rate,
                            AltLocationsFlag = "Y",
                            PositionName = job.Name,
                        });

                        jobUpdate = true;
                    }
                }

                if (employeeUpdate || jobUpdate)
                {
                    if (executionMode == ExecutionMode.Execute)
                    {
                        Log.Logger.Information("Updating employee {cseq}/{fn} {ln} @ {loc}, jobs: {jcnt}", employee.ClockSeq, employee.FirstName,
                        employee.LastName, employee.PrimaryWorkLocation, fullStoreEmployeeRecord.EmployeePositions?.Count);

                        if (fullStoreEmployeeRecord.EmployeePositions?.Count < currentJobCount)
                        {
                            Log.Logger.Fatal("Employee {cseq}/{fn} {ln} @ {loc} new job count {jcnt} is less than current job count {exp}, skipping update.",
                                employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation,
                                fullStoreEmployeeRecord.EmployeePositions?.Count, currentJobCount);
                            return false;
                        }

                        service.SaveEmployeeAsync(fullStoreEmployeeRecord).Wait();
                    }
                    else
                    {
                        Log.Logger.Information("Would update employee {cseq}/{fn} {ln} @ {loc}", employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                        //debug log the fullStore
                    }

                    return true;
                }

                return false;
            });

            ConsoleService.PrintFormattedJson(results);
        }

        public void Terms(List<string> args)
        {
            var results = new List<Result>();
            var appConfig = new AppConfig();

            ProcessImportCommand(appConfig.TermMode, args, true, "Term", appConfig.TermLimit,
            (CrunchTimeService service, string location, IDictionary<string, CrunchTimeEmployee> employeeDirectory, ExecutionMode executionMode, Payroll.Shared.Employee employee) =>
            {
                if (employee.Active) return false; // only interested in terminated employees

                if (employee.TermDate > DateTime.Now)
                {
                    Log.Logger.Information("Skipping termination of employee {cseq} with future term date {date}",
                        employee.ClockSeq, employee.TermDate?.ToShortDateString());
                    return false;
                }

                CrunchTimeEmployee fullStoreEmployeeRecord = null;
                {
                    // The main purpose of this call is to see if the employee record is in the scope of locations that we are supposed to be processing.
                    if (!employeeDirectory.TryGetValue(employee.ClockSeq, out CrunchTimeEmployee ctEmployee))
                    {
                        Log.Logger.Information("Skipping termination of a non-existent employee {id}/{cseq} - {fn} {ln} @ loc {loc}",
                            employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                        return false;
                    }

                    if (ctEmployee.Status == "Terminated")
                    {
                        Log.Logger.Information("  Employee {id}/{cseq} - {fn} {ln} @ loc {loc} is already terminated, skipping...",
                            employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, employee.PrimaryWorkLocation);
                        return false;
                    }

                    fullStoreEmployeeRecord = service.GetEmployeeDetailsAsync(ctEmployee.EmployeeNumber, false).Result;
                    if (fullStoreEmployeeRecord == null)
                    {
                        Log.Logger.Error("Failed to get employee details from CrunchTime for employee {id}", ctEmployee.EmployeeNumber);
                        return false;
                    }
                }

                //TODO  status change, and reason, and date of termination.
                    Dictionary<string, string> attrs = employee.Attributes;
                var terminationReason = appConfig.StdTerminationReason ?? employee.Description;
                Log.Logger.Information("Terminating employee {0}/{1} - {2} {3} for {4}", employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, terminationReason);

                if (executionMode == ExecutionMode.Execute)
                {
                    fullStoreEmployeeRecord.Status = "Terminated";
                    fullStoreEmployeeRecord.StatusCode = "Terminated";
                    fullStoreEmployeeRecord.TerminationReason = terminationReason;
                    fullStoreEmployeeRecord.DateTerminated = employee.TermDate.Value.ToString("MM/dd/yyyy");
                    service.SaveEmployeeAsync(fullStoreEmployeeRecord).Wait();
                }
                else
                {
                    Log.Logger.Information("Would have terminated employee {0}/{1} - {2} {3} on {4} for {5}", employee.Id, employee.ClockSeq, employee.FirstName, employee.LastName, employee.TermDate?.ToShortDateString(), terminationReason);
                }

                return true;

            });

            ConsoleService.PrintFormattedJson(results);
        }


    }
}
