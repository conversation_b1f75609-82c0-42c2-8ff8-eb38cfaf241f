using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Reflection;
using System.Threading.Tasks;
using System.Text.Json;
using System.Text.Json.Serialization;

using Serilog;
using Payroll.Shared;
using System.Linq;

namespace CrunchTime.Tool;

public class CacheableFetchOptions
{
    public bool UseCache { get; set; } = true;
    public bool IncludeTerminated { get; set; } = false;

    public static CacheableFetchOptions Default() => new CacheableFetchOptions();
}

public class CrunchTimeService : IDisposable
{
    public static AppConfig AppConfig = new AppConfig();
    private int TotalApiRequestsMade { get; set; } = 0;
    private DateTime StartTime { get; } = DateTime.Now;

    private HttpRequestMessage BuildRequestFor(string authToken, string path)
    {
        TotalApiRequestsMade++;
        var fullUrl = $"{AppConfig.Endpoint}/{path}";

        var request = new HttpRequestMessage
        {
            Method = HttpMethod.Get,
            RequestUri = new Uri(fullUrl),
            Headers =
            {
                { "accept", "application/json" },
                { "authenticationtoken", authToken },
                { "sitename", AppConfig.Sitename() },
                { "userid", AppConfig.Username() },
                { "password", AppConfig.Password() },
            },
        };

        //Log.Logger.Debug(JsonSerializer.Serialize(request.Headers));
        Log.Logger.Debug("Request URL: {url}", fullUrl);
        return request;
    }

    public async Task<Location> GetLocationAsync(string locationCode)
    {
        using var client = new HttpClient();
        var path = $"location/v1/getAllLocations?locationCode={locationCode}";
        Log.Logger.Debug("Fetching location from API: {path}", path);

        using var request = BuildRequestFor(AppConfig.LocationApiKey(), path);
        using var response = await client.SendAsync(request);

        response.EnsureSuccessStatusCode();
        var responseBody = await response.Content.ReadAsStringAsync();
        Log.Logger.Debug($"Response Body: {responseBody}");

        var crunchTimeLocations = JsonSerializer.Deserialize<List<Location>>(responseBody);
        return crunchTimeLocations?.FirstOrDefault();
    }

    public async Task<List<Payroll.Shared.Location>> GetLocationsAsync(CacheableFetchOptions options)
    {
        var locations = new List<Payroll.Shared.Location>();

        if (options.UseCache)
        {
            Log.Logger.Debug("Fetching locations from cache...");
            locations = CacheService.FetchRecords<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION).ToList();
            if (locations != null && locations.Count > 0)
            {
                if (!options.IncludeTerminated)
                {
                    locations.RemoveAll(x => !x.Active);
                }
                Log.Logger.Debug("Locations found in cache: {count}", locations.Count);
                return locations;
            }
        }

        using var client = new HttpClient();
        var activeOnly = options.IncludeTerminated ? "false" : "true";
        var path = $"location/v1/getAllLocations?activeFlag={activeOnly}";
        Log.Logger.Debug("Fetching locations from API: {path}", path);

        using var request = BuildRequestFor(AppConfig.LocationApiKey(), path);
        using var response = await client.SendAsync(request);

        response.EnsureSuccessStatusCode();
        var responseBody = await response.Content.ReadAsStringAsync();
        Log.Logger.Debug($"Response Body: {responseBody}");

        var crunchTimeLocations = JsonSerializer.Deserialize<List<Location>>(responseBody);
        List<Location> franchiseLocations = new List<Location>();

        foreach (var ctLocation in crunchTimeLocations)
        {
            if (AppConfig.SkipLocation(ctLocation.LocationCode))
            {
                Log.Logger.Warning("Skipping location {locationCode} as it is in the skip list", ctLocation.LocationCode);
                continue;
            }

            var locAddrDetail = ctLocation.LocationNameAddressDetails.FirstOrDefault();
            var locationAltAddressDetail = ctLocation.LocationAltAddressDetails.FirstOrDefault();

            if (AppConfig.StrictMode && string.IsNullOrEmpty(locationAltAddressDetail.PayrollExportCode))
            {
                Log.Logger.Warning("Location {locationCode} has no payroll export code", ctLocation.LocationCode);
                continue;
            }

            if (locAddrDetail.FranchiseCode == AppConfig.Franchise || AppConfig.Franchise == "*")
            {
                franchiseLocations.Add(ctLocation);
            }
        }

        locations = Converter.ConvertLocations(AppConfig, franchiseLocations);

        // get cached location data if available
        using var locationService = new LocationService();
        foreach (var location in locations)
        {
            var success = await locationService.DecorateLocationWithTimeZoneInfo(AppConfig.POS_LOCATIONS_CACHE_COLLECTION, location);
            if (!success)
            {
                Log.Logger.Warning("Failed to decorate location {id} with timezone info", location.Id);
            }
        }

        return locations;
    }

    public async Task<List<Payroll.Shared.Location>> GetActiveLocationsAsync(CacheableFetchOptions options = null)
    {
        options ??= CacheableFetchOptions.Default();
        options.IncludeTerminated = false;
        return await GetLocationsAsync(options);
    }

    public async Task<string> GetUsersAsync()
    {
        using var client = new HttpClient();
        using var request = BuildRequestFor(AppConfig.UserApiKey(), "applicationuser/v1/getAllApplicationUsers");
        using var response = await client.SendAsync(request);

        response.EnsureSuccessStatusCode();
        return await response.Content.ReadAsStringAsync();
    }

    public async Task<List<TimeClockEnhancedDetailDetails>> GetTimeClockDetailsAsync(string locationCode, DateOnly laborDate, string employeeNumber = null)
    {
        try
        {
            using var client = new HttpClient();
            var formattedDate = laborDate.ToString("dd-MMM-yy", System.Globalization.CultureInfo.InvariantCulture);

            var path = $"timeclock/v1/getAllTimeClockEnhanced?laborDate={formattedDate}&locationCode={locationCode}";
            if (!string.IsNullOrEmpty(employeeNumber))
            {
                path += $"&employeeNumber={employeeNumber}";
            }

            using var request = BuildRequestFor(AppConfig.ClockApiKey(), path);
            using var response = await client.SendAsync(request);

            response.EnsureSuccessStatusCode();
            var responseBody = await response.Content.ReadAsStringAsync();
            Log.Logger.Debug("Response Body: {body}", responseBody);

            var result = JsonSerializer.Deserialize<List<TimeClockEnhancedDetailsResponse>>(responseBody);
            return result?.FirstOrDefault()?.TimeClockEnhancedDetailDetails ?? new List<TimeClockEnhancedDetailDetails>();
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Failed to get time clock details for location {locationCode} on {laborDate}", locationCode, laborDate);
            return new List<TimeClockEnhancedDetailDetails>();
        }
    }

    public async Task<IEnumerable<string>> GetEmployeeNumbersAsync(string locationCode, bool includeTerminated=true)
    {
        using var client = new HttpClient();
        using var request = BuildRequestFor(AppConfig.EmpApiKey(), $"employee/v1/getEmployeeNumbers?locationCode={locationCode}&isIncludeTerminated={includeTerminated}");
        using var response = await client.SendAsync(request);

        response.EnsureSuccessStatusCode();
        var responseBody = await response.Content.ReadAsStringAsync();
        var employeeNumbers = JsonSerializer.Deserialize<List<string>>(responseBody);
        //Log.Logger.Debug("Employee Numbers: {numbers}", string.Join(", ", employeeNumbers));
        return employeeNumbers;
    }

    public async Task<IEnumerable<string>> GetActiveEmployeeNumbersAsync(string locationCode)
    {
        return await GetEmployeeNumbersAsync(locationCode, includeTerminated: false);
    }

    public async Task<CrunchTimeEmployee> GetEmployeeDetailsAsync(string employeeNumber, bool useCache = true)
    {
        if (useCache)
        {
            var employee = CacheService.FetchRecord<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION, employeeNumber);
            if (employee != null)
            {
                //Log.Logger.Debug($"Employee {employeeNumber} found in cache");
                return employee;
            }
        }

        using var client = new HttpClient();
        using var request = BuildRequestFor(AppConfig.EmpApiKey(), $"employee/v1/detail?employeeNumber={employeeNumber}");

        var responseBody = await TryFetchJson(client, request);
        if (string.IsNullOrEmpty(responseBody))
        {
            Log.Logger.Debug("Failed to fetch employee details for {employeeNumber}", employeeNumber);
            return null;
        }

        CrunchTimeEmployee employeeDetails;
        try
        {
            employeeDetails = JsonSerializer.Deserialize<CrunchTimeEmployee>(responseBody);
            CacheService.CacheRecord<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION, employeeDetails);
            return employeeDetails;
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error deserializing or caching employee details for {employeeNumber}", employeeNumber);
            Log.Logger.Error(responseBody);
            throw;
        }
    }

    public async Task<Dictionary<string, CrunchTimeEmployee>> GetCrunchTimeEmployeesAsync(string locationCode, CacheableFetchOptions options = null)
    {
        options ??= CacheableFetchOptions.Default();
        var employees = new Dictionary<string, CrunchTimeEmployee>();

        try
        {
            var employeeNumbers = await GetEmployeeNumbersAsync(locationCode, options.IncludeTerminated);

            foreach (var employeeNumber in employeeNumbers)
            {
                // Get employee details for each employee number
                //Log.Logger.Debug($"Getting employee details for {employeeNumber}");
                var crunchTimeEmployee = await GetEmployeeDetailsAsync(employeeNumber, options.UseCache);
                if (crunchTimeEmployee == null)
                {
                    Log.Logger.Warning("Failed to fetch employee details for {employeeNumber}", employeeNumber);
                    continue;
                }
                employees[employeeNumber] = crunchTimeEmployee;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error fetching employees for location {locationCode}", locationCode);
        }

        return employees;
    }

    public async Task<Dictionary<string, Dictionary<string, CrunchTimeEmployee>>> GetAllEmployeesAsync(CacheableFetchOptions options = null)
    {
        options ??= CacheableFetchOptions.Default();
        var employeeDirectory = new Dictionary<string, Dictionary<string, CrunchTimeEmployee>>();

        try
        {
            // Get all active locations
            var activeLocations = await GetActiveLocationsAsync();

            foreach (var location in activeLocations)
            {
                employeeDirectory[location.Code] = await GetCrunchTimeEmployeesAsync(location.Code, options);
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error fetching active employees");
        }

        return employeeDirectory;
    }

    /// <summary>
    /// Saves an employee to the CrunchTime system. This method can be used for both creating a new employee
    /// and updating an existing employee's information.
    /// </summary>
    /// <param name="crunchTimeEmployee">The CrunchTimeEmployee object containing the employee data to save.</param>
    /// <returns>A boolean indicating whether the save operation was successful.</returns>
    public async Task<bool> SaveEmployeeAsync(CrunchTimeEmployee crunchTimeEmployee)
    {
        try
        {
            using var client = new HttpClient();
            var path = "employee/v1/saveEmployee";
            using var request = BuildRequestFor(AppConfig.EmpApiKey(), path);

            var crunchTimeEmployeeRequest = new CrunchTimeEmployeeRequest
            {
                Employee = new List<CrunchTimeEmployee>
                {
                    crunchTimeEmployee
                },
                FranchiseSite = AppConfig.Franchise
            };

            var options = new JsonSerializerOptions
            {
                DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull,
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            };
            var content = JsonSerializer.Serialize(crunchTimeEmployeeRequest, options);

            Log.Logger.Debug("Request Body: {body}", content);
            request.Content = new StringContent(content, System.Text.Encoding.UTF8, "application/json");
            request.Method = HttpMethod.Post;

            // Add these lines to log the full request details
            Log.Logger.Debug("Request URI: {Uri}", request.RequestUri);
            Log.Logger.Debug("Request Method: {Method}", request.Method);
            Log.Logger.Debug("Request Headers: {Headers}", string.Join(", ", request.Headers.Select(h => $"{h.Key}: {string.Join(", ", h.Value)}")));

            using var response = await client.SendAsync(request);
            var responseBody = await response.Content.ReadAsStringAsync();

            Log.Logger.Debug("Response Status Code: {StatusCode}", response.StatusCode);
            Log.Logger.Debug("Response Body: {ResponseBody}", responseBody);

            if (!response.IsSuccessStatusCode)
            {
                Log.Logger.Error("HTTP request failed with status code {StatusCode}. Response body: {ResponseBody}", response.StatusCode, responseBody);
                return false;
            }

            try
            {
                if (response.StatusCode == System.Net.HttpStatusCode.OK)
                {
                    Log.Logger.Information("HTTP request succeeded with status code 200");
                    return true;
                }

                if (string.IsNullOrWhiteSpace(responseBody))
                {

                    Log.Logger.Warning("Response body is empty");
                    return false;
                }

                var result = JsonSerializer.Deserialize<List<SaveEmployeeResponse>>(responseBody);

                if (result != null && result.Count > 0)
                {
                    var firstResponse = result[0];
                    if (firstResponse.errors == null || firstResponse.errors.Count == 0)
                    {
                        Log.Logger.Information("Successfully saved employee {EmployeeId}", crunchTimeEmployee.EmployeeNumber);
                        return true;
                    }
                    else
                    {
                        foreach (var error in firstResponse.errors)
                        {
                            Log.Logger.Error("Error saving employee {EmployeeId}: Line {LineNumber}, Field {FieldNumber} - {ErrorMessage}",
                                crunchTimeEmployee.EmployeeNumber, error.lineNumber, error.fieldNumber, error.errorMessage);
                        }
                        return false;
                    }
                }
                else
                {
                    Log.Logger.Error("Unexpected response format when saving employee {EmployeeId}", crunchTimeEmployee.EmployeeNumber);
                    return false;
                }
            }
            catch (JsonException jsonEx)
            {
                Log.Logger.Error(jsonEx, "Failed to deserialize response. Response body: {ResponseBody}", responseBody);
                return false;
            }
        }
        catch (Exception ex)
        {
            Log.Logger.Error(ex, "Error saving employee {EmployeeId}", crunchTimeEmployee.EmployeeNumber);
            return false;
        }
    }

    private async Task<string> TryFetchJson(HttpClient client, HttpRequestMessage request)
    {
        const int maxRetries = 3;
        const int retryDelaySeconds = 10;

        // Clone the original request
        var originalRequest = request;

        for (int attempt = 1; attempt <= maxRetries; attempt++)
        {
            try
            {
                // Create a new request for each attempt
                var currentRequest = new HttpRequestMessage(originalRequest.Method, originalRequest.RequestUri);
                foreach (var header in originalRequest.Headers)
                {
                    currentRequest.Headers.Add(header.Key, header.Value);
                }
                currentRequest.Content = originalRequest.Content;

                Log.Logger.Debug(messageTemplate: "Fetching {url} (Attempt {attempt}/{maxRetries})", currentRequest.RequestUri, attempt, maxRetries);

                var response = await client.SendAsync(currentRequest);

                if (response.IsSuccessStatusCode)
                {
                    return await response.Content.ReadAsStringAsync();
                }

                if (!response.StatusCode.IsRetryable())
                {
                    Log.Logger.Debug($"Non-retriable error: {currentRequest.RequestUri} => {response.StatusCode}");
                    return null;
                }

                Log.Logger.Error($"Retriable error: {currentRequest.RequestUri} => {response.StatusCode}");

                if (attempt < maxRetries)
                {
                    Log.Logger.Warning($"Retrying in {retryDelaySeconds} seconds...");
                    await Task.Delay(retryDelaySeconds * 1000);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error($"Attempt {attempt}/{maxRetries} failed: {e.Message}");

                if (attempt < maxRetries)
                {
                    Log.Logger.Warning($"Retrying in {retryDelaySeconds} seconds...");
                    await Task.Delay(retryDelaySeconds * 1000);
                }
                else
                {
                    throw; // Re-throw if all retries failed
                }
            }
        }

        return string.Empty;
    }

    public void Dispose()
    {
        var endTime = DateTime.Now;
        var totalTime = endTime - StartTime;
        var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

        Log.Logger.Debug("Total Api Requests Made = {total}", TotalApiRequestsMade);
        Log.Logger.Debug("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

        if (requestsPerSec > 15)
            Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
        else
            Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
    }
}
