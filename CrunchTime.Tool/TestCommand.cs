using System;
using System.Globalization;
using System.Collections.Generic;
using Payroll.Shared;
using Serilog;
using System.Linq;

namespace CrunchTime.Tool;

public class TestCommand
{
    private Dictionary<string, Payroll.Shared.Location> LocationMap = new Dictionary<string, Payroll.Shared.Location>();

    public void Parse(string[] args)
    {
        if (args.Length < 1)
        {
            Console.WriteLine("Usage: crunchtime test parse <dateString>");
            return;
        }

        var dateString = args[0];
        var format = (args.Length > 1) ? args[1] : "HH:mm MM/dd/yyyy ZZZ";

        if (DateTimeOffset.TryParseExact(dateString, format, CultureInfo.InvariantCulture, DateTimeStyles.None, out DateTimeOffset result))
        {
            Console.WriteLine(result);
        }
        else
        {
            Console.WriteLine($"Invalid date string '{dateString}' for format: '{format}'");
        }
    }

    public void Directory(List<string> args)
    {
        try
        {
            if (args == null || args.Count < 1)
            {
                Console.WriteLine("Usage: CrunchTime.Tool.exe test directory <store-number|all>");
                return;
            }

            string unitId = args[0];
            var forceSync = Environment.GetEnvironmentVariable("FORCE_SYNC") == "1";

            Log.Logger.Information("Getting employee directory for location: {loc}, ForceSync: {fsync}", unitId, forceSync);

            using (var service = new CrunchTimeService())
            {
                var locations = service.GetActiveLocationsAsync().Result;
                if (unitId != "all") locations.RemoveAll(x => x.Code != unitId);
                LocationMap = locations.ToDictionary(x => x.Code);

                var employeeDirectory = new Dictionary<string, Dictionary<string, CrunchTimeEmployee>>();
                var cacheOptions = new CacheableFetchOptions() { IncludeTerminated = forceSync };

                foreach (var location in locations)
                {
                    Log.Logger.Information("Fetching employees for location: {loc}", location.Code);
                    var employees = service.GetCrunchTimeEmployeesAsync(location.Code, cacheOptions).Result;
                    employeeDirectory.Add(location.Code, employees);
                    Log.Logger.Information("Found {count} employees for location {loc}", employees.Count, location.Code);
                }

                // Output the employee directory as JSON
                var output = new
                {
                    Locations = locations.Select(l => new { l.Code, l.Name, l.State }),
                    EmployeeDirectory = employeeDirectory.ToDictionary(
                        kvp => kvp.Key,
                        kvp => kvp.Value.ToDictionary(
                            emp => emp.Key,
                            emp => new
                            {
                                emp.Value.EmployeeNumber,
                                emp.Value.PayrollIdNumber,
                                emp.Value.FirstName,
                                emp.Value.LastName,
                                emp.Value.Status,
                                emp.Value.StatusCode,
                                emp.Value.PrimaryLocationCode,
                                JobCount = emp.Value.EmployeePositions?.Count ?? 0
                            }
                        )
                    )
                };

                ConsoleService.PrintFormattedJson(output);
            }
        }
        catch (Exception e)
        {
            Log.Logger.Fatal(e.Message);
            if (!string.IsNullOrEmpty(e.StackTrace)) Log.Logger.Fatal(e.StackTrace);
        }
    }
}
