using CommandLine;
using LiteDB;
using Payroll.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Globalization;
using System.Reflection;

namespace CrunchTime.Tool
{
    [AttributeUsage(AttributeTargets.Assembly)]
    internal class BuildDateAttribute : Attribute
    {
        public BuildDateAttribute(string value)
        {
            DateTime = DateTime.ParseExact(value, "yyyyMMddHHmmss", CultureInfo.CurrentCulture, DateTimeStyles.None);
        }

        public DateTime DateTime { get; }
    }

    class Program : ProgramBase2
    {
        static readonly string AppVersion = "0.2"; // hire date window

        private static DateTime GetBuildDate()
        {
            var assembly = Assembly.GetExecutingAssembly();
            var attribute = assembly.GetCustomAttribute<BuildDateAttribute>();
            return attribute?.DateTime ?? default(DateTime);
        }

        static int DoShowUsage()
        {
            Console.WriteLine("Usage: CrunchTime.Tool.exe <command> <command-args>");
            Console.WriteLine($"  Branch: {BuildInfo.Branch}, Built: {BuildInfo.Built}, Host: {BuildInfo.Host}, Hash: {BuildInfo.Commit}");
            Console.WriteLine();

            Console.WriteLine("  where <command> is one of...");
            Console.WriteLine("   - cache [clear|dump|employee|init|update|location|locations|view]");
            Console.WriteLine("   - employee [active|terms|list] <unit-id|all>");
            Console.WriteLine("   - export [all-time|time]");
            Console.WriteLine("   - import [hires|terms|sync] <unit-id|all>");
            Console.WriteLine("   - info - show version and tool information");
            Console.WriteLine("   - location [active|hidden|list|enable <location-id>|disable <location-id>]");
            Console.WriteLine("   - test [parse|directory] <args>");

            return 0;
        }

        public override int ShowUsage()
        {
            return DoShowUsage();
        }

        public void Help(List<string> args)
        {
            ShowUsage();
        }

        public void Info(List<string> args)
        {
            var appConfig = new AppConfig();
            Console.WriteLine($"Crunchtime.Tool.exe, version: {AppVersion}, Built: {GetBuildDate()}");
            Console.WriteLine();
            Console.WriteLine($"  Settings:");
            Console.WriteLine($"    Franchise    = {appConfig.Franchise}");
            Console.WriteLine($"    User Api Key = {appConfig.UserApiKey()}");
            Console.WriteLine($"    Username     = {appConfig.Username()}");
            Console.WriteLine($"    Sitename     = {appConfig.Sitename()}");
            Console.WriteLine($"    Modify Payroll IDs = {appConfig.ModifyPayrollIds}");
            Console.WriteLine($"    New Hire Days Window = {appConfig.NewHireDaysWindow}");
            Console.WriteLine();
            Console.WriteLine("  Fields:");
            Console.WriteLine($"    Payroll ID   = {FieldService.PayrollIdField}");
        }

        private int ExecCommand<T>(List<string> args) where T : new()
        {
            var command = new CommandArguments(args);

            return Command<T>.Invoke(command);
        }

        public int Audit(List<string> args)
        {
            return ExecCommand<AuditCommand>(args);
        }

        public void Cache(List<string> args)
        {
            ExecCommand<CacheCommand>(args);
        }

        public int Employee(List<string> args)
        {
            return ExecCommand<EmployeeCommand>(args);
        }

        public void Export(List<string> args)
        {
            ExecCommand<ExportCommand>(args);
        }

        public void Location(List<string> args)
        {
            ExecCommand<LocationCommand>(args);
        }

        public void Import(List<string> args)
        {
            ExecCommand<ImportCommand>(args);
        }

        public void Test(List<string> args)
        {
            ExecCommand<TestCommand>(args);
        }

        public void User(List<string> args)
        {
            ExecCommand<UserCommand>(args);
        }

        private static void SetupCache()
        {
            // setup database services...
            var mapper = BsonMapper.Global;
            mapper.Entity<CrunchTimeEmployee>().Id(x => x.EmployeeNumber);

            // setup cache service indices...
            try
            {
                using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
                {
                    var empCache = cache.GetCollection<CrunchTimeEmployee>(AppConfig.POS_EMPLOYEES_CACHE_COLLECTION);
                    empCache.EnsureIndex(x => x.EmployeeNumber);

                    var locationCache = cache.GetCollection<Payroll.Shared.Location>(AppConfig.POS_LOCATIONS_CACHE_COLLECTION);
                    locationCache.EnsureIndex(x => x.Code);
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }
        }

        static int Main(string[] args)
        {
            if (args.Length == 0)
            {
                DoShowUsage();
                return -1;
            }

            // setup logging services...
            var command = string.Join(" ", args);
            Logger.Setup($"CrunchTime.Tool, Command: '{command}', Version: {AppVersion}");

            // Log version information
            Log.Logger.Information($"Crunchtime.Tool.exe, version: {AppVersion}, Built: {GetBuildDate()}");

            SetupCache();

            try
            {
                Parser.Default.ParseArguments<ProgramArguments>(args)
                    .MapResult((ProgramArguments opts) => ProgramDriver<Program>.Run(opts), errs => 1);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
                return -1;
            }

            return 0;
        }
    }
}
