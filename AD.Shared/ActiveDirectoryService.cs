using Payroll.Shared;
using Serilog;
using SharpCompress.Common;
using System.Diagnostics.CodeAnalysis;
using System.DirectoryServices;
using System.DirectoryServices.AccountManagement;
using System.IO;
using System.Linq;
using System.Net.WebSockets;
using System.Security.Cryptography;

namespace AD.Shared
{
    [System.Diagnostics.CodeAnalysis.SuppressMessage("Interoperability", "CA1416:Validate platform compatibility",
                Justification = "Only supporting Windows Platforms")]
    public class ActiveDirectoryService : IDisposable
    {
        DirectoryEntry _directoryRoot;

        public ActiveDirectoryService()
        {
            _directoryRoot = new DirectoryEntry(Config.Endpoint());
            _directoryRoot.Username = Config.Username();
            _directoryRoot.Password = Config.Password();
        }

        public bool IsActiveUser(DirectoryEntry de)
        {
            if (de.NativeGuid == null) return false;

            var userAccountControlValue = de.Properties["userAccountControl"].Value;
            if (userAccountControlValue == null) return false;

            if (userAccountControlValue is int flags)
            {
                return !Convert.ToBoolean(flags & 0x0002);
            }

            return false;
        }

        public Employee? ConvertDirectoryEntryToEmployee(DirectoryEntry? dEntry)
        {
            if (dEntry == null) return null;

            var employee = new Employee()
            {
                WorkEmail = dEntry.Properties["mail"].Value?.ToString(),
                FirstName = dEntry.Properties["givenName"].Value?.ToString(),
                LastName = dEntry.Properties["sn"].Value?.ToString(),
                CityAddress = dEntry.Properties["l"].Value?.ToString(),
                StreetAddress = dEntry.Properties["streetAddress"].Value?.ToString(),
                State = dEntry.Properties["st"].Value?.ToString(),
                Zip = dEntry.Properties["postalCode"].Value?.ToString(),
                DeptName = dEntry.Properties["department"].Value?.ToString(),
                Description = dEntry.Properties["description"].Value?.ToString(),
                OrgName = dEntry.Properties["company"].Value?.ToString(),
                OfficePhone = dEntry.Properties["extensionAttribute5"].Value?.ToString(),
                Id = dEntry.Properties[Config.PrimaryKey()].Value?.ToString(),
                ClockSeq = dEntry.Properties["employeeID"].Value?.ToString(),
            };

            employee.Active = IsActiveUser(dEntry);

            employee.AddAttribute("division_name", dEntry.Properties["division"].Value?.ToString());
            employee.AddAttribute("gl_code", dEntry.Properties["extensionAttribute3"].Value?.ToString().Head(7));
            employee.AddAttribute("business_title", dEntry.Properties["title"].Value?.ToString());

            var cityMobile = dEntry.Properties["mobile"].Value?.ToString();
            if (!string.IsNullOrEmpty(cityMobile)) employee.AddAttribute("city_mobile", cityMobile);
            var officePhone = dEntry.Properties["officephone"].Value?.ToString();
            if (!string.IsNullOrEmpty(officePhone)) employee.AddAttribute("city_phone", officePhone);

            // Get manager distinguished name and check for null/empty before lookup
            string? managerDn = dEntry.Properties["manager"].Value?.ToString();
            DirectoryEntry? managerDentry = null;
            if (!string.IsNullOrEmpty(managerDn))
            {
                managerDentry = FindEntryByDistinguishedName(managerDn);
            }

            if (managerDentry != null)
            {
                employee.AddAttribute("manager", managerDentry.Properties["extensionAttribute11"].Value?.ToString());
            }

            return employee;
        }

        public EmployeeDirectoryEntry? ConvertToEmployeeDirectoryEntry(DirectoryEntry? dEntry)
        {
            if (dEntry == null) return null;

            var samAccountName = dEntry.Properties["samAccountName"].Value?.ToString();
            if (samAccountName?.Length > 20)
            {
                samAccountName = samAccountName.Substring(0, 20);
                Log.Logger.Warning("SamAccountName '{0}' was truncated to '{1}' to meet 20 character limit.",
                    dEntry.Properties["samAccountName"].Value, samAccountName);
            }

            var employee = new EmployeeDirectoryEntry()
            {
                Mail = dEntry.Properties["mail"].Value?.ToString(),
                GivenName = dEntry.Properties["givenName"].Value?.ToString(),
                SurName = dEntry.Properties["sn"].Value?.ToString(),
                City = dEntry.Properties["city"].Value?.ToString(),
                StreetAddress = dEntry.Properties["streetAddress"].Value?.ToString(),
                State = dEntry.Properties["state"].Value?.ToString(),
                EmployeeId = dEntry.Properties["employeeID"].Value?.ToString(),
                EmployeeNumber = dEntry.Properties["employeeNumber"].Value?.ToString(),
                PostalCode = dEntry.Properties["postalCode"].Value?.ToString(),
                Department = dEntry.Properties["department"].Value?.ToString(),
                DivisionName = dEntry.Properties["division"].Value?.ToString(),
                Description = dEntry.Properties["description"].Value?.ToString(),
                Company = dEntry.Properties["company"].Value?.ToString(),
                OfficePhone = dEntry.Properties["telephoneNumber"].Value?.ToString(),
                ExtensionAttribute1 = dEntry.Properties["extensionAttribute1"].Value?.ToString(),
                ExtensionAttribute2 = dEntry.Properties["extensionAttribute2"].Value?.ToString(),
                ExtensionAttribute3 = dEntry.Properties["extensionAttribute3"].Value?.ToString(),
                ExtensionAttribute4 = dEntry.Properties["extensionAttribute4"].Value?.ToString(),
                ExtensionAttribute5 = dEntry.Properties["extensionAttribute5"].Value?.ToString(),
                ExtensionAttribute6 = dEntry.Properties["extensionAttribute6"].Value?.ToString(),
                ExtensionAttribute7 = dEntry.Properties["extensionAttribute7"].Value?.ToString(),
                ExtensionAttribute8 = dEntry.Properties["extensionAttribute8"].Value?.ToString(),
                ExtensionAttribute9 = dEntry.Properties["extensionAttribute9"].Value?.ToString(),
                ExtensionAttribute10 = dEntry.Properties["extensionAttribute10"].Value?.ToString(),
                ExtensionAttribute11 = dEntry.Properties["extensionAttribute11"].Value?.ToString(),
                Title = dEntry.Properties["title"].Value?.ToString(),
                PersonalTitle = dEntry.Properties["Personal Title"].Value?.ToString(),
                MobilePhone = dEntry.Properties["mobile"].Value?.ToString(),
                ParentOU = dEntry.Parent.Path,
                SamAccountName = samAccountName
            };

            // Get manager distinguished name and check for null/empty before lookup
            string? managerDn = dEntry.Properties["manager"].Value?.ToString();
            DirectoryEntry? managerDentry = null;
            if (!string.IsNullOrEmpty(managerDn))
            {
                managerDentry = FindEntryByDistinguishedName(managerDn);
            }

            if (managerDentry != null)
            {
                employee.Manager = managerDentry.Properties["extensionAttribute11"].Value?.ToString();
            }

            employee.Active = IsActiveUser(dEntry);

            var memberOf = dEntry.Properties["memberOf"].Value;
            if (memberOf != null)
            {
                if (memberOf.GetType() == typeof(string))
                {
                    employee.MemberOf.Add((string)memberOf);
                }
                else if (memberOf.GetType() == typeof(object[]))
                {
                    foreach (var group in (object[])memberOf)
                    {
                        employee.MemberOf.Add(group.ToString());
                    }
                }
            }

            return employee;
        }

        private DirectoryEntry? GetDirectoryEntry(string filter)
        {
            try
            {
                Log.Logger.Debug("Searching AD for filter: {f}", filter);
                DirectorySearcher directorySearcher = new(_directoryRoot);
                directorySearcher.Filter = filter;

                SearchResult? searchResult = directorySearcher.FindOne();

                if (searchResult != null)
                {
                    return searchResult.GetDirectoryEntry();
                }
            }
            catch (Exception ex)
            {
                Log.Logger.Warning("Could not find entry with filter: " + filter + " in AD.");
            }

            return null;
        }

        public DirectoryEntry? GetUserDirectoryEntryBySamAccountName(string samaccountname)
        {
            return GetDirectoryEntry($"(&(objectClass=user)(objectCategory=person)(samaccountname={samaccountname}))");
        }

        public DirectoryEntry? GetUserDirectoryEntryByPrimaryKey(string pkey)
        {
            var pkey_name = Config.PrimaryKey();
            return GetDirectoryEntry($"(&(objectClass=user)(objectCategory=person)({pkey_name}={pkey}))");
        }

        public Employee? GetEmployee(string samaccountname)
        {
            var user = GetUserDirectoryEntryBySamAccountName(samaccountname);
            return ConvertDirectoryEntryToEmployee(user);
        }

        public EmployeeDirectoryEntry? GetEmployeeDirectoryEntry(string samaccountname)
        {
            var user = GetUserDirectoryEntryBySamAccountName(samaccountname);
            if (user != null)
            {
                Log.Logger.Debug("Parent OU: {0}", user.Parent.Path);
                Log.Logger.Debug("DN: {0}", user.Path);
            }
            return ConvertToEmployeeDirectoryEntry(user);
        }

        private string RelativePathFromRoot(string path)
        {
            var endPt = Config.Endpoint();
            if (path.StartsWith(Config.Endpoint()) && path.Length > endPt.Length)
            {
                // the plus one is for the separating slash
                path = path.Substring(endPt.Length + 1);
            }

            return path;
        }

        private bool ResetPropertyToDefault(DirectoryEntry dEntry, string propname)
        {
            if (string.IsNullOrEmpty(propname)) return false;
            PropertyValueCollection? property = dEntry.Properties[propname];

            if (property.Value != null)
            {
                Log.Logger.Information("Resetting '{0}': from '{1}' to null", property.PropertyName, property.Value);
                property.Value = null;
            }

            return true;
        }

        private bool UpdateOrAddPropertyOnChange(DirectoryEntry dEntry, string propname, string propvalue)
        {
            if (string.IsNullOrEmpty(propvalue)) return false;

            PropertyValueCollection? property = dEntry.Properties[propname];
            if (property == null)
            {
                Log.Logger.Warning("Missing property '{0}', adding and setting to {1}", propname, propvalue);
                dEntry.Properties[propname].Add(propvalue);
                return true;
            }

            if (property.Value == null || (string)property.Value != propvalue)
            {
                Log.Logger.Information("Updating '{0}': from '{1}' to '{2}'", property.PropertyName, property.Value, propvalue);
                property.Value = propvalue;
                return true;
            }

            return false;
        }

        private bool UpdateOrAddPropertyRecordChange(DirectoryEntry dEntry, string propname, string newValue, ICollection<Change> changes)
        {
            if (string.IsNullOrEmpty(newValue)) return false;

            PropertyValueCollection? property = dEntry.Properties[propname];

            var change = new Change()
            {
                PropertyName = property.PropertyName,
                NewValue = newValue,
                PrimaryKey = dEntry.Name
            };

            if (property == null || property.Value == null)
            {
                Log.Logger.Warning("Missing or empty property '{0}', setting to '{1}' for {2}", propname, newValue, dEntry.Name);
                dEntry.Properties[propname].Add(newValue);
            }
            else
            {
                if ((string)property.Value == newValue) return false;

                string oldValue = property.Value.ToString();
                Log.Logger.Information("Updating '{0}': from '{1}' to '{2}' for {3}", property.PropertyName, oldValue, newValue, dEntry.Name);

                property.Value = newValue;
                change.OldValue = oldValue;
            }

            changes.Add(change);
            return true;
        }

        private bool UpdateChangedProperties(DirectoryEntry user, EmployeeDirectoryEntry employee, ICollection<Change> changeLog)
        {
            UpdateOrAddPropertyRecordChange(user, "mail", employee.Mail, changeLog);
            UpdateOrAddPropertyRecordChange(user, "givenName", employee.GivenName, changeLog);
            UpdateOrAddPropertyRecordChange(user, "sn", employee.SurName, changeLog);
            UpdateOrAddPropertyRecordChange(user, "l", employee.City, changeLog);
            UpdateOrAddPropertyRecordChange(user, "streetAddress", employee.StreetAddress, changeLog);
            UpdateOrAddPropertyRecordChange(user, "st", employee.State, changeLog);
            UpdateOrAddPropertyRecordChange(user, "postalCode", employee.PostalCode, changeLog);
            UpdateOrAddPropertyRecordChange(user, "department", employee.Department, changeLog);
            UpdateOrAddPropertyRecordChange(user, "division", employee.DivisionName, changeLog);
            UpdateOrAddPropertyRecordChange(user, "employeeNumber", employee.EmployeeNumber, changeLog);
            UpdateOrAddPropertyRecordChange(user, "employeeID", employee.EmployeeId, changeLog);
            UpdateOrAddPropertyRecordChange(user, "description", $"{employee.SurName}, {employee.GivenName}", changeLog);
            UpdateOrAddPropertyRecordChange(user, "company", employee.Company, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute1", employee.ExtensionAttribute1, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute2", employee.ExtensionAttribute2, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute3", employee.ExtensionAttribute3, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute4", employee.ExtensionAttribute4, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute5", employee.ExtensionAttribute5, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute6", employee.ExtensionAttribute6, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute7", employee.ExtensionAttribute7, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute8", employee.ExtensionAttribute8, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute9", employee.ExtensionAttribute9, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute10", employee.ExtensionAttribute10, changeLog);
            UpdateOrAddPropertyRecordChange(user, "extensionAttribute11", employee.ExtensionAttribute11, changeLog);
            UpdateOrAddPropertyRecordChange(user, "mobile", employee.MobilePhone, changeLog);
            UpdateOrAddPropertyRecordChange(user, "title", employee.Title, changeLog);
            UpdateOrAddPropertyRecordChange(user, "Personal Title", employee.PersonalTitle, changeLog);
            UpdateOrAddPropertyRecordChange(user, "telephoneNumber", employee.OfficePhone, changeLog);

            if (string.IsNullOrEmpty(employee.Manager))
            {
                Log.Logger.Debug($"No manager in import for user: {employee.PrimaryKey} - {employee.GivenName} {employee.SurName}.");
            }
            else
            {
                var managerDentry = GetUserDirectoryEntryByPrimaryKey(employee.Manager);
                if (managerDentry != null)
                {
                    var mdRelPath = RelativePathFromRoot(managerDentry.Path);
                    UpdateOrAddPropertyRecordChange(user, "manager", mdRelPath, changeLog);
                }
            }

            return changeLog.Count > 0;
        }

        public ICollection<Change> UpdateEmployee(EmployeeDirectoryEntry employee, ExecutionMode executionMode)
        {
            DirectoryEntry? user = GetUserDirectoryEntryByPrimaryKey(employee.PrimaryKey);
            ICollection<Change> changeLog = new List<Change>();

            if (user == null)
            {
                Log.Logger.Debug("Could not find user: {pkey} - {GivenName} {SurName} ({dept}) in AD.",
                    employee.PrimaryKey, employee.GivenName, employee.SurName, employee.Department);
                return changeLog;
            }

            bool changes = UpdateChangedProperties(user, employee, changeLog);

            // Commit changes
            if (changes && executionMode == ExecutionMode.Execute)
            {
                user.CommitChanges();
                Log.Logger.Information("Updated user: {0} ({1}/{2}) in AD.", employee.SamAccountName,
                    employee.EmployeeId, employee.PrimaryKey);
            }

            // now move the user if needed
            if (!string.IsNullOrEmpty(employee.ParentOU))
            {
                DirectoryEntry? parentOU = FindEntryByDistinguishedName(employee.ParentOU);
                if (parentOU != null && user.Parent.Path != parentOU.Path)
                {
                    Log.Logger.Information("Moving user {0} ({1}/{2}) from {3} to {4}... (DISABLED FOR NOW)",
                        employee.SamAccountName, employee.EmployeeId, employee.PrimaryKey, user.Parent.Path, employee.ParentOU);
                    //if (executionMode == ExecutionMode.Execute) user.MoveTo(parentOU);
                }
            }

            return changeLog;
        }

        public bool UpdateGroupMemberships(EmployeeDirectoryEntry employee, ExecutionMode executionMode)
        {
            DirectoryEntry? user = GetUserDirectoryEntryByPrimaryKey(employee.PrimaryKey);

            if (string.IsNullOrEmpty(employee.PolicyDN))
            {
                ThrowArgumentNullException("PolicyDN", employee.EmployeeId, employee.SamAccountName);
            }

            try
            {
                if (employee.Department.StartsWith("Police", StringComparison.OrdinalIgnoreCase))
                {
                    Log.Logger.Information("  Police department job titles changes are not supported, skipping...");
                    return false;
                }

                DirectoryEntry? templateDN = FindEntryByDistinguishedName(employee.PolicyDN);
                if (templateDN == null)
                {
                    Log.Logger.Error("Could not find template DN: " + employee.PolicyDN + " in AD.");
                    return false;
                }

                var groupsWeShouldNotRemove = ListGroupsMemberOf(templateDN);

                // remove current group memberships, then add new ones
                if (!RemoveGroupMemberships(groupsWeShouldNotRemove, user, executionMode))
                {
                    Log.Logger.Error("Remove Group Memberships failed for user {0} ({1})", employee.EmployeeId, employee.SamAccountName);
                    return false;
                }

                // skip system groups for existing employees (skipGroup true)
                CopyGroupMemberships(templateDN, user, executionMode);
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                return false;
            }

            return true;
        }

        public bool RemoveGroupMemberships(ICollection<string> otherGroupsToSkip,
                                            DirectoryEntry from, ExecutionMode executionMode)
        {
            var relativeFromPath = RelativePathFromRoot(from.Path);
            object[]? groups = (object[]?)from.Properties["memberOf"].Value;

            Log.Logger.Information("RemoveGroupMemberships from {0}", from.Name);
            foreach (var group in otherGroupsToSkip)
            {
                Log.Logger.Information($"  Will skip: {group}");
            }

            foreach (var grp in groups)
                {
                    if (grp == null) continue;

                    var grpDN = (string)grp;
                    var groupEntry = FindEntryByDistinguishedName(grpDN);
                    if (groupEntry == null)
                    {
                        Log.Logger.Error("Could not find group {0} in AD.", grp);
                        continue;
                    }

                    if (otherGroupsToSkip.Contains(grpDN))
                    {
                        Log.Logger.Information("NEW: Would have skipped group |{0}| for removal...", grpDN);
                    }

                    var grpCommonName = groupEntry.Name;
                    if (grpCommonName.StartsWith("CN="))
                    {
                        var chunks = grpCommonName.Split('=');
                        var groupName = chunks[1];

                        if (Config.SkipGroup(groupName))
                        {
                            Log.Logger.Information("Skipping group |{0}| for removal...", groupName);
                            continue;
                        }
                    }

                    try
                    {
                        Log.Logger.Information("RemoveGroupMemberships: WOULD remove {0} as member of |{1}| in AD.", from.Name, groupEntry.Name);
                        if (executionMode == ExecutionMode.Execute)
                        {
                            groupEntry.Properties["member"].Remove(relativeFromPath);
                            groupEntry.CommitChanges();
                        }
                    }
                    catch (Exception e)
                    {
                        Log.Logger.Error(e.Message);
                        return false;
                    }
                }

            return true;
        }

        private HashSet<string> ListGroupsMemberOf(DirectoryEntry templateDN)
        {
            var groupsInMembership = new HashSet<string>();

            var memberOf = templateDN.Properties["memberOf"].Value;
            if (memberOf == null)
            {
                Log.Logger.Error("Could not find any groups for {0} in AD", templateDN.Path);
                return groupsInMembership;
            }

            if (memberOf.GetType() == typeof(string))
            {
                groupsInMembership.Add((string)memberOf);
            }
            else
            {
                object[]? groups = (object[]?)memberOf;
                if (groups == null)
                {
                    Log.Logger.Error("Could not find any groups for {0} in AD.", templateDN.Path);
                    return groupsInMembership;
                }

                foreach (object group in groups)
                {
                    groupsInMembership.Add((string)group);
                }
            }

            return groupsInMembership;
        }

        public bool CopyGroupMemberships(DirectoryEntry from, DirectoryEntry to, ExecutionMode executionMode, bool skipGroups = true)
        {
            var relativeToPath = RelativePathFromRoot(to.Path);

            var fromGroupMemberOf = ListGroupsMemberOf(from);
            var toGroupMemberOf = ListGroupsMemberOf(to);

            foreach (var grp in fromGroupMemberOf)
            {
                var groupEntry = FindEntryByDistinguishedName(grp);

                if (groupEntry == null)
                {
                    Log.Logger.Error("Could not find group {0} in AD.", grp);
                    continue;
                }

                var relativeGroupPath = RelativePathFromRoot(groupEntry.Path);
                if (toGroupMemberOf.Contains(relativeGroupPath))
                {
                    Log.Logger.Debug("Already a member of {0}, skipping...", groupEntry.Name);
                    continue;
                }

                // optional support for skipping certain system groups
                // ROCKHILL: used for employee transfers only, not new hires. Unsure of how other clients would want this to work.
                if (skipGroups)
                {
                    var grpCommonName = groupEntry.Name;
                    if (grpCommonName.StartsWith("CN="))
                    {
                        var chunks = grpCommonName.Split('=');
                        var groupName = chunks[1];

                        if (Config.SkipGroup(groupName))
                        {
                            Log.Logger.Information("Skipping group |{0}| for copying...", groupName);
                            continue;
                        }
                    }
                }

                try
                {
                    Log.Logger.Information("CopyGroupMemberships: adding {0} as member of {1} in AD...", to.Name, groupEntry.Name);
                    if (executionMode == ExecutionMode.Execute)
                    {
                        groupEntry.Properties["member"].Add(relativeToPath);
                        groupEntry.CommitChanges();
                    }
                }
                catch (Exception e)
                {
                    Log.Logger.Error(e.Message);
                }
            }

            return true;
        }

        private bool CopyProperty(string propname, DirectoryEntry from, DirectoryEntry to)
        {
            if (string.IsNullOrEmpty(propname)) return false;

            var fromProperty = from.Properties[propname];
            if (fromProperty == null || fromProperty.Value == null) return false;

            var toProperty = to.Properties[propname];
            if (toProperty == null)
            {
                Log.Logger.Information("Adding '{0}' from template: '{1}'", propname, fromProperty.Value);
                to.Properties[propname].Add(fromProperty.Value);
            }
            else
            {
                // these checks limit the copying of properties to only true changes
                if (toProperty.Value != null)
                {
                    if (toProperty.Value.GetType() == typeof(string) && toProperty.Value.Equals(fromProperty.Value)) return false;

                    if (toProperty.Value.GetType() == typeof(Object[]))
                    {
                        var toArray = toProperty.Value as string[];
                        var fromArray = fromProperty.Value as string[];

                        if (toArray.SequenceEqual(fromArray))
                        {
                            return false;
                        }
                    }
                }

                Log.Logger.Information("Copying '{0}' from template: '{1}'", propname, fromProperty.Value);
                toProperty.Value = fromProperty.Value;
            }

            return true;
        }

        private void CopyTemplateProperties(DirectoryEntry from, DirectoryEntry to)
        {
            CopyProperty("protocolSettings", from, to);
            CopyProperty("showInAddressBook", from, to);
            CopyProperty("telephoneNumber", from, to);
        }

        private void ThrowArgumentNullException(string paramName, string employeeId, string samAccountName)
        {
            string errorMessage = $"{paramName} missing, cannot create {employeeId}/{samAccountName}";
            Log.Logger.Fatal(errorMessage);
            var ex = new ArgumentNullException(paramName, errorMessage);
            throw ex;
        }

        public bool AddEmployee(EmployeeDirectoryEntry employee, ExecutionMode executionMode)
        {
            // Ensure SamAccountName is not more than 20 characters
            string samAccountName = employee.SamAccountName;
            if (samAccountName.Length > 20)
            {
                samAccountName = samAccountName.Substring(0, 20);
                Log.Logger.Warning("SamAccountName '{0}' was truncated to '{1}' to meet 20 character limit.",
                    employee.SamAccountName, samAccountName);
            }

            if (string.IsNullOrEmpty(employee.ParentOU))
            {
                ThrowArgumentNullException("ParentOU", employee.EmployeeId, samAccountName);
            }

            if (string.IsNullOrEmpty(employee.PolicyDN))
            {
                ThrowArgumentNullException("PolicyDN", employee.EmployeeId, samAccountName);
            }

            var parent = FindEntryByDistinguishedName(employee.ParentOU);

            if (parent == null)
            {
                Log.Logger.Error("Could not find parent OU: " + employee.ParentOU + " in AD.");
                return false;
            }

            DirectoryEntry? templateDN = FindEntryByDistinguishedName(employee.PolicyDN);
            if (templateDN == null)
            {
                Log.Logger.Error("Could not find template DN: " + employee.PolicyDN + " in AD.");
                return false;
            }

            // make sure this employee doesn't already exist
            bool addedNewRecord = true;
            var newHire = GetUserDirectoryEntryBySamAccountName(samAccountName);
            if (newHire != null)
            {
                Log.Logger.Warning("User {0} already exists, but will continue...", samAccountName);
                addedNewRecord = false;
            }
            else
            {
                DirectoryEntries children = parent.Children;
                string name = employee.Name.EscapeCharacter(',', '\\');
                newHire = children.Add($"CN={name}", "user");

                UpdateOrAddPropertyOnChange(newHire, "sAMAccountName", samAccountName);
            }

            CopyTemplateProperties(templateDN, newHire);

            // Set userPrincipalName (this is not the same as their email address)
            // Use the full SamAccountName here, not the truncated version
            UpdateOrAddPropertyOnChange(newHire, "userPrincipalName", $"{employee.SamAccountName}@cityofrockhillsc.gov");

            // Commit changes
            Log.Logger.Information("{op} new user: {0}", addedNewRecord? "Adding" : "Updating", newHire.Path);

            if (executionMode == ExecutionMode.Execute)
            {
                try
                {
                    newHire.CommitChanges();
                }
                catch (System.DirectoryServices.DirectoryServicesCOMException e)
                {
                    Log.Logger.Error($"Error committing changes: {e.Message}");
                    Log.Logger.Error($"Error code: {e.ErrorCode}");
                    Log.Logger.Error($"Extended error: {e.ExtendedError}");
                    Log.Logger.Error($"Extended error message: {e.ExtendedErrorMessage}");
                    // Handle the error appropriately
                    return false;
                }

                parent.CommitChanges();
                newHire.RefreshCache();
            }

            // make sure user starts enabled
            if (newHire.Properties["userAccountControl"]?.Value != null)
            {
                int uacflags = newHire.Properties["userAccountControl"] == null ? 0 : (int)newHire.Properties["userAccountControl"].Value;
                newHire.Properties["userAccountControl"].Value = uacflags & ~0x2;
            }

            ICollection<Change> changeLog = new List<Change>();
            UpdateChangedProperties(newHire, employee, changeLog);
            if (executionMode == ExecutionMode.Execute)
                newHire.CommitChanges();

            // Copy group memberships
            // DO NOT skip system groups for new hires
            CopyGroupMemberships(templateDN, newHire, executionMode, false);

            return addedNewRecord;
        }

        public bool AddPolicyGroups(EmployeeDirectoryEntry employee, ExecutionMode executionMode)
        {
            if (string.IsNullOrEmpty(employee.PolicyDN))
            {
                ThrowArgumentNullException("PolicyDN", employee.EmployeeId, employee.SamAccountName);
            }

            DirectoryEntry? templateDN = FindEntryByDistinguishedName(employee.PolicyDN);
            if (templateDN == null)
            {
                Log.Logger.Error("Could not find template DN: " + employee.PolicyDN + " in AD.");
                return false;
            }

            // make sure this employee doesn't already exist
            var employeeDirEntry = GetUserDirectoryEntryBySamAccountName(employee.SamAccountName);
            if (employeeDirEntry == null)
            {
                Log.Logger.Warning("User {0} not found", employee.SamAccountName);
                return false;
            }

            // Copy group memberships
            CopyGroupMemberships(templateDN, employeeDirEntry, executionMode);

            return true;
        }

        public void DisableUser(string samAccountName)
        {
            var user = GetUserDirectoryEntryBySamAccountName(samAccountName);

            if (user == null)
            {
                Log.Logger.Error("Failed to find user: {0}", samAccountName);
                return;
            }

            var uacFlags = user.Properties["userAccountControl"].Value;
            if (uacFlags == null)
            {
                Log.Logger.Error($"Could not find UCA for: {user.Username} in AD.");
                return;
            }

            // Set the user account's enabled property to false.
            user.Properties["userAccountControl"].Value = ((int)uacFlags) | 0x2;

            // Commit the property changes to Active Directory.
            user.CommitChanges();
        }

        public bool TermEmployee(EmployeeDirectoryEntry employee, ExecutionMode executionMode)
        {
            DirectoryEntry? user = GetUserDirectoryEntryByPrimaryKey(employee.PrimaryKey);

            if (user == null)
            {
                Log.Logger.Warning($"Could not find user: {employee.PrimaryKey} - {employee.SamAccountName} in AD.");
                return false;
            }

            if (user.Properties.Contains("isDeleted"))
            {
                Log.Logger.Information($"User: {employee.PrimaryKey} - {employee.SamAccountName} is already deleted in AD.");
                return false;
            }

            var uacFlags = user.Properties["userAccountControl"].Value;
            if (uacFlags == null)
            {
                Log.Logger.Information($"Could not find UCA for: {employee.PrimaryKey} - {employee.SamAccountName} in AD.");
                return false;
            }

            // Get the value of the user account's userAccountControl attribute.
            int userAccountControl = (int)uacFlags;

            // Check if the ADS_UF_ACCOUNTDISABLE flag is set.
            bool terminatedOrMoved = false;
            if ((userAccountControl & 0x2) != 0x2)
            {
                Log.Logger.Information("Found terminated user with active account: {0} ({1}) in AD, terminated {2}, disabling...",
                    employee.SamAccountName, employee.EmployeeNumber, employee.TermDate?.ToShortDateString());

                // Set the user account's enabled property to false.
                user.Properties["userAccountControl"].Value = ((int)uacFlags) | 0x2;

                ICollection<Change> changeLog = new List<Change>();
                UpdateOrAddPropertyRecordChange(user, "description", employee.Description, changeLog);

                // update some additional properties as needed...
                foreach (var prop in Config.SensitiveProps)
                {
                    ResetPropertyToDefault(user, prop);
                }

                // remove all group memberships from the terminated user... (no groups to skip!)
                var groupsToSkip = new List<string>();
                RemoveGroupMemberships(groupsToSkip, user, executionMode);

                // Commit the property changes to Active Directory.
                if (executionMode == ExecutionMode.Execute) user.CommitChanges();
                terminatedOrMoved = true;
            }

            // now move the user if needed
            if (string.IsNullOrEmpty(employee.ParentOU)) return terminatedOrMoved;

            DirectoryEntry? parentOU = FindEntryByDistinguishedName(employee.ParentOU);
            if (parentOU != null && user.Parent.Path != parentOU.Path)
            {
                Log.Logger.Information("Moving terminated user to {0}...", employee.ParentOU);

                if (executionMode == ExecutionMode.Execute)
                    user.MoveTo(parentOU);
                terminatedOrMoved = true;
            }

            return terminatedOrMoved;
        }

        public SearchResultCollection ListGroups(DirectoryEntry root)
        {
            //bind to the native AdsObject to search
            DirectorySearcher search = new DirectorySearcher(root);

            //configure search filter
            search.Filter = "(objectClass=group)";

            //configure search to find all objects in domain
            search.SearchScope = SearchScope.Subtree;

            //run search
            return search.FindAll();
        }

        public SearchResultCollection ListGroupsAll()
        {
            //bind to the native AdsObject to search
            return ListGroups(_directoryRoot);
        }

        public SearchResultCollection ListEmployees()
        {
            //bind to the native AdsObject to search
            DirectorySearcher search = new DirectorySearcher(_directoryRoot);

            //configure search filter
            search.Filter = "(&(objectClass=user)(objectCategory=person))";

            //configure search to find all objects in domain
            search.SearchScope = SearchScope.Subtree;

            //run search
            return search.FindAll();
        }

        public IEnumerable<EmployeeDirectoryEntry> SearchEmployeesByLastName(string lastname)
        {
            //bind to the native AdsObject to search
            DirectorySearcher search = new DirectorySearcher(_directoryRoot);

            //configure search filter
            search.Filter = $"(&(objectClass=user)(objectCategory=person)(sn={lastname}*))";

            //configure search to find all objects in domain
            search.SearchScope = SearchScope.Subtree;

            //run search
            SearchResultCollection results = search.FindAll();

            //iterate over results and print names
            var matches = new List<EmployeeDirectoryEntry>();
            foreach (SearchResult result in results)
            {
                DirectoryEntry user = result.GetDirectoryEntry();
                var ede = ConvertToEmployeeDirectoryEntry(user);
                if (ede != null) matches.Add(ede);
            }

            return matches;
        }

        public EmployeeDirectoryEntry? FindEntryByCommonName(string cn)
        {
            //bind to the native AdsObject to search
            var search = new DirectorySearcher(_directoryRoot);
            search.Filter = "(cn=" + cn + ")";

            // Find the directory entry.
            var result = search.FindOne();

            if (result == null)
            {
                Log.Logger.Warning("The directory entry with the common name '{0}' could not be found.", cn);
                return null;
            }

            DirectoryEntry user = result.GetDirectoryEntry();
            foreach (string propertyName in user.Properties.PropertyNames)
                Console.WriteLine($"{propertyName}: {user.Properties[propertyName][0]}");

            return ConvertToEmployeeDirectoryEntry(user);
        }

        public DirectoryEntry? FindEntryByDistinguishedName(string dn)
        {
            if (string.IsNullOrEmpty(dn)) return null;

            var search = new DirectorySearcher(_directoryRoot);
            search.Filter = "(&(distinguishedName=" + dn + "))";

            // Find the directory entry.
            var result = search.FindOne();

            if (result == null)
            {
                Log.Logger.Warning("The directory entry with the distinguished name '{0}' could not be found.", dn);
                return null;
            }

            return result.GetDirectoryEntry();
        }

        void IDisposable.Dispose()
        {
        }
    }
}
