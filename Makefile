HOSTNAME=$(shell hostname)
MACHINE_OS?=linux8
COMMIT_HASH_FILE=Payroll.Shared/BuildInfo.cs
NOW:=$(shell date "+%y%m%d%H%M")
COMMIT_HASH:=$(shell git rev-parse HEAD)
CURRENT_BRANCH:=$(shell git branch --show-current)
SHORT_HASH:=$(shell git rev-parse --short HEAD)
SEMVER:=$(shell dotnet-gitversion /output json /showvariable SemVer 2>/dev/null || echo "unknown")

setup:
	rm -rf dist/*
	echo "public static class BuildInfo { " > $(COMMIT_HASH_FILE);
	echo "  public const string Branch = \"$(CURRENT_BRANCH)\";" >> $(COMMIT_HASH_FILE);
	echo "  public const string Built = \"$(shell date "+%m-%d-%Y @ %H:%M%p")\";" >> $(COMMIT_HASH_FILE);
	echo "  public const string Host=\"$(HOSTNAME)\";" >> $(COMMIT_HASH_FILE)
	echo "  public const string Commit=\"$(COMMIT_HASH)\";" >> $(COMMIT_HASH_FILE)
	echo "}" >> $(COMMIT_HASH_FILE)

test:
	$(MAKE) setup
	(cd Brink.Tool && make clean $(MACHINE_OS))

slim:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd CrunchTime.Tool && make clean $(MACHINE_OS))
	(cd Paycom2.Tool && make clean $(MACHINE_OS))

slim-corp:
	$(MAKE) slim
	(cd Clients/SlimChicken-Corp/SlimChicken.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/SlimChicken-Corp/Scripts/*.ini dist/configs
	cp -r Clients/SlimChicken-Corp/Scripts/*.sh dist/
	mkdir -p ../Releases/SlimChicken-Corp/
	find ../Releases/SlimChicken-Corp/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/SlimChicken-Corp/

ap-group:
	$(MAKE) MACHINE_OS=linux9 slim
	(cd Clients/Ap-Group/ApGroup.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/Ap-Group/Scripts/*.ini dist/configs
	cp -r Clients/Ap-Group/Scripts/*.sh dist/
	mkdir -p ../Releases/Ap-Group/
	find ../Releases/Ap-Group/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Ap-Group/

slim-4j:
	$(MAKE) slim
	(cd Aloha.Tool && make clean $(MACHINE_OS))
	(cd Clients/SlimChicken-FourJays/FourJays.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Common/Scripts/*.timer dist/configs
	cp -r Clients/Common/Scripts/*.service dist/configs
	cp -r Clients/SlimChicken-FourJays/Scripts/*.* dist/configs
	mkdir -p ../Releases/SlimChicken-FourJays/
	find ../Releases/SlimChicken-FourJays/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/SlimChicken-FourJays/

graeters:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Paycom2.Tool && make clean $(MACHINE_OS))
	(cd DataCentral.Tool && make clean $(MACHINE_OS))
	(cd Clients/Graeters/Graeters.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Graeters/Scripts/*.ini dist/configs
	cp -r Clients/Graeters/Scripts/*.timer dist/configs
	cp -r Clients/Graeters/Scripts/*.service dist/configs
	cp -r Clients/Graeters/Scripts/*.sh dist/
	mkdir -p ../Releases/Graeters/
	find ../Releases/Graeters/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Graeters/

upward:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Toast.Tool && make clean $(MACHINE_OS))
	(cd Paylocity.Tool && make clean $(MACHINE_OS))
	(cd Clients/Upward/Upward.Tool && make $(MACHINE_OS))
	mkdir dist/configs
	cp -r Clients/Upward/Scripts/*.ini dist/configs
	cp -r Clients/Upward/Scripts/*.sh dist/
	mkdir -p ../Releases/Upward/
	find ../Releases/Upward/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -vr dist/* ../Releases/Upward/

rockhill:
	$(MAKE) setup
	(cd Payroll.Tool && make win8)
	(cd AD.Tool && make win8)
	(cd Paycom2.Tool && make win8)
	(cd Clients/RockHill/Tool && make win8)
	mkdir dist/Scripts
	cp -r Clients/RockHill/Scripts/* dist/Scripts/
	cp -r Clients/RockHill/Scripts/* dist/
	mkdir -p ../Releases/RockHill/
	find ../Releases/RockHill/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -r dist/* ../Releases/RockHill/

artbrew:
	$(MAKE) setup
	(cd Payroll.Tool && make clean $(MACHINE_OS))
	(cd Toast.Tool && make clean $(MACHINE_OS))
	(cd Clients/ArtBrew/Tool && make clean $(MACHINE_OS))
	mkdir dist/Scripts
	cp -r Clients/ArtBrew/Scripts/* dist/Scripts/
	cp -r Clients/ArtBrew/Scripts/*.sh dist/
	mkdir -p ../Releases/ArtBrew/
	find ../Releases/ArtBrew/ -mindepth 1 -type d -not -path '*/\.*' -prune | xargs rm -rf
	cp -r dist/* ../Releases/ArtBrew/

clean:
	find . -type d -name "bin" | xargs rm -rf && find . -type d -name "obj" | xargs rm -rf
	rm -rf ../Releases/Graeters/*
	rm -rf ../Releases/ArtBrew/*
	rm -rf ../Releases/SlimChicken/*
	rm -rf ../Releases/RockHill/*
	rm -rf ../Releases/SlimChicken-FourJays/*
	rm -rf ../Releases/SlimChicken-Corp/*

publish-upward:
	rclone sync --no-update-modtime -P ../Releases/Upward/ r2:upward/PayrollTools/

publish-artbrew:
	rclone sync --no-update-modtime -P ../Releases/ArtBrew/ r2:artbrew/

publish-rockhill:
	rclone sync --no-update-modtime -P ../Releases/RockHill/ r2:rockhill/

publish-graeters:
	rclone sync --no-update-modtime -P ../Releases/Graeters/ r2:graeters/PayrollTools

publish-slim-corp:
	rclone sync --no-update-modtime -P ../Releases/SlimChicken-Corp/ r2:slim/Corp

publish-slim-4jays:
	rclone sync --no-update-modtime -P ../Releases/SlimChicken-FourJays/ r2:slim/FourJays

publish-ap-group:
	rclone sync --no-update-modtime -P ../Releases/Ap-Group/ r2:ap-group

upward-container:
	$(MAKE) upward
	# Set SemVer variable with fallback and build/push Docker container
	echo "Building $(SEMVER)-$(SHORT_HASH)";
	# Build and push the Docker container with all tools from repository root
	podman buildx build . -f Clients/Upward/Dockerfile -t ghcr.io/oa-labs/upwards-ptools:$(SEMVER)-$(SHORT_HASH) --build-arg PAYROLL_DIST=dist/payroll --build-arg TOAST_DIST=dist/toast --build-arg PAYLOCITY_DIST=dist/paylocity --build-arg CLIENT_DIST=dist/client --platform linux/amd64

artbrew-container:
	$(MAKE) artbrew
	# Set SemVer variable with fallback and build/push Docker container
	echo "Building $(SEMVER)-$(SHORT_HASH)";
	# Build and push the Docker container with all tools from repository root
	podman buildx build --add-host payroll-tools:127.0.0.1 . -f Clients/ArtBrew/Dockerfile -t ghcr.io/oa-labs/artbrew-ptools:$(SEMVER)-$(SHORT_HASH) --build-arg PAYROLL_DIST=dist/payroll --build-arg TOAST_DIST=dist/toast --build-arg CLIENT_DIST=dist/client --platform linux/amd64

container-registry-login:
	@echo "Checking GitHub Container Registry authentication..."
	@if ! podman login --get-login ghcr.io >/dev/null 2>&1; then \
		echo "Error: Not authenticated with ghcr.io"; \
		echo "Please ensure GITHUB_USERNAME and GITHUB_TOKEN environment variables are set"; \
		echo "and rebuild the devcontainer, or run: echo \$$GITHUB_TOKEN | podman login ghcr.io -u \$$GITHUB_USERNAME --password-stdin"; \
		exit 1; \
	fi

container-ptools-version:
	@echo "****************************************************************"
	@echo "ptools-set-version $(SEMVER)-$(SHORT_HASH) to update production"
	@echo "****************************************************************"

upward-container-push:
	$(MAKE) container-registry-login
	podman push -q ghcr.io/oa-labs/upwards-ptools:$(SEMVER)-$(SHORT_HASH)
	$(MAKE) container-ptools-version

artbrew-container-push:
	$(MAKE) container-registry-login
	podman push -q ghcr.io/oa-labs/artbrew-ptools:$(SEMVER)-$(SHORT_HASH)
	$(MAKE) container-ptools-version
