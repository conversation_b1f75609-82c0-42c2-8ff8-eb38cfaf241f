#!/bin/bash

# Function to truncate files in a directory to last 100,000 lines
truncate_files() {
    local directory="$1"
    
    # Check if directory exists
    if [ ! -d "$directory" ]; then
        echo "Error: Directory '$directory' does not exist"
        return 1
    fi
    
    # Iterate over all files in the directory
    for file in "$directory"/*; do
        # Check if it's a regular file
        if [ -f "$file" ]; then
            echo "Processing: $file"
            # Store original ownership and permissions
            owner=$(stat -c '%u:%g' "$file")
            perms=$(stat -c '%a' "$file")

            # Create a temporary file
            temp_file=$(mktemp)
            # Keep last 100,000 lines and write to temp file
            tail -n 100000 "$file" > "$temp_file"

            # Replace original file with truncated version
            mv "$temp_file" "$file"
            
            # Restore original ownership and permissions
            chown "$owner" "$file"
            chmod "$perms" "$file"
        fi
    done
    
    echo "Processing complete for $directory"
}

# Call function for each directory
truncate_files "/opt/openarc/Logs/Paycom"
truncate_files "/opt/openarc/Logs/Crunch"

echo "All directories processed"

echo "Cleaning journal and snap cruft"

journalctl --vacuum-size=100M

LANG=en_US.UTF-8 snap list --all | awk '/disabled/{print $1, $3}' |
while read pkg revision; do
  snap remove "$pkg" --revision="$revision"
done