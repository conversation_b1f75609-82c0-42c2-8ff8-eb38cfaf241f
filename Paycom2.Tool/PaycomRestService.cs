using Payroll.Shared;
using Paycom2.Shared;
using Serilog;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using System.Text.RegularExpressions;
using MongoDB.Driver;
using Location = Paycom2.Shared.Location;
using System.Text;

namespace Paycom2.Tool
{
    public class PaycomRestService : IDisposable
    {
        readonly string ConfigTimeZone = Config.TimeZone();
        static bool ConfigHasPrimaryJob = Config.HasPrimaryJob();
        static bool HasCustomFields;
        private static readonly int ApiDelayInSeconds = 0;
        private static uint TotalApiRequestsMade { get; set; }
        private static Dictionary<string, uint> ApiCallCounter { get; set; }
        private DateTime StartTime { get; set; }

        Dictionary<string, DirectoryEntry> EmployeeDirectory { get; set; }
        Dictionary<int, Location> LocationDirectory { get; set; }
        Dictionary<string, LaborAllocationDetail> LaborAllocationDetails { get; set; }

        private AuthenticationHeaderValue AuthenticationToken { get; set; }
        private static AuthenticationHeaderValue PaycomAuthenticationHeader { get; }

        static PaycomRestService()
        {
            // cache authorization body
            var authenticationString = $"{Config.PaycomSid()}:{Config.PaycomToken()}";
            var base64EncodedAuthenticationString = Convert.ToBase64String(System.Text.ASCIIEncoding.ASCII.GetBytes(authenticationString));
            //Log.Logger.Debug("Fetching locations via {url}", base64EncodedAuthenticationString);
            PaycomAuthenticationHeader = new AuthenticationHeaderValue("Basic", base64EncodedAuthenticationString);
            HasCustomFields = Config.CustomFields.Count > 0;
        }

        public PaycomRestService()
        {
            TotalApiRequestsMade = 0;
            StartTime = DateTime.Now;
            ApiCallCounter = new Dictionary<string, uint>();
            Init();
        }

        public void Init()
        {
            LocationDirectory = new Dictionary<int, Location>();
            LaborAllocationDetails = new Dictionary<string, LaborAllocationDetail>();

            Log.Logger.Debug("SID: {sid}", Config.PaycomSid());
            Log.Logger.Debug("Token: {token}", Config.PaycomToken());
        }

        public Dictionary<int, Location> GetLocationDirectory()
        {
            if (LocationDirectory == null || LocationDirectory.Count == 0)
            {
                try
                {
                    // load locations
                    var locations = GetLocationsAsync().Result;
                    foreach (var loc in locations)
                    {
                        LocationDirectory.Add(loc.locationid, loc);
                    }
                }
                catch (Exception e)
                {
                    Log.Logger.Fatal(e.Message);
                    Log.Logger.Fatal(e.StackTrace);
                }
            }
            return LocationDirectory;
        }

        private async Task<uint> LoadEmployeeDirectory(uint fetchMax = 0)
        {
            // only load once
            if (EmployeeDirectory != null) return 0;
            EmployeeDirectory = new Dictionary<string, DirectoryEntry>();

            uint counter = 0;

            try
            {
                // load employee directory
                var employees = await AllEmployeesAsync(fetchMax);

                foreach (var e in employees)
                {
                    if (string.IsNullOrEmpty(e.eecode))
                    {
                        Log.Logger.Warning("Skipping employee with no employee_code");
                        continue;
                    }

                    EmployeeDirectory.Add(e.eecode, e);
                    counter++;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return counter;
        }

        public Payroll.Shared.Employee ConvertNewHireRecordToEmployee(NewHire e)
        {
            var emp = new Payroll.Shared.Employee()
            {
                Id = e.newEmployeeCode,
                ClockSeq = e.clockSequenceNumber,
                FirstName = e.firstName,
                LastName = e.lastName,
                StreetAddress = e.address,
                CityAddress = e.city,
                State = e.state,
                Zip = e.zip,
                DateOfBirth = e.birthDate,
                OfficePhone = e.primaryPhone,
                CellPhone = e.secondaryPhone,
                Active = true,
            };

            emp.AddAttribute("manager_name", e.primarySupervisor);
            //emp.AddAttribute("manager_code", e.pr);
            emp.AddAttribute("business_title", e.positionTitle);
            emp.AddAttribute("middle_name", e.middleName);
            emp.AddAttribute("badge_no", e.badgeNumber);

            return emp;
        }

        static public Employee ConvertDirectoryEntryToEmployee(DirectoryEntry e)
        {
            var employee = new Payroll.Shared.Employee()
            {
                Id = e.eecode,
                ClockSeq = e.clockseq,
                FirstName = e.firstname.ToProperCase(),
                LastName = e.lastname.ToProperCaseWithOddCaps(),
                StreetAddress = e.streetaddr,
                CityAddress = e.cityaddr,
                State = e.homestate,
                Zip = e.zipcode.ToString(),
                Active = Converter.IsActiveEmployee(e.eestatus),
                Description = $"[{e.deptcode}]-{e.cat1desc}-{e.cat2desc}-{e.cat3desc}"
            };

            return employee;
        }

        public async Task<Employee> ConvertMasterEmployeeRecordToEmployee(MasterEmployeeRecord e)
        {
            var employee = new Payroll.Shared.Employee()
            {
                Id = e.employee_code,
                ClockSeq = e.clocksequencenumber,
                FirstName = FieldService.GetFirstName(e),
                LastName = e.lastname.ToProperCaseWithOddCaps(),
                StreetAddress = e.street,
                CityAddress = e.city,
                State = e.state,
                Zip = e.zipcode.ToString(),
                OfficePhone = e.primary_phone,
                CellPhone = e.secondary_phone,
                Active = Converter.IsActiveEmployee(e.employee_status),
                DateOfBirth = e.birth_date,
                Salaried = e.pay_class == "SAL"
            };

            if (employee.Active)
                employee.Description = e.dol_status;
            else
                employee.Description = e.termination_type; // used in termination logic occasionally

            await AddDetailedDataToEmployee(employee, e);

            // if a new employee never shows up, set the term date to the hire date (Paycom doesn't set it)
            if (e.employee_status == MasterEmployeeRecord.NotHiredStatus && employee.TermDate == null)
            {
                employee.TermDate = employee.HireDate;
            }

            return employee;
        }

        protected async Task<string> GetEmployeeJsonAsync(string eecode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));
                Log.Logger.Debug("Fetching employee json via {url}", endpoint);
                var json = await client.GetStringAsync(endpoint);

                LogApiRequest(apiMethod);
                return json;
            }
        }
        public async Task<JsonElement> GetEmployeeCustomFieldsJsonAsync(string eecode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}/customfield";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));
                Log.Logger.Debug("Fetching employee custom fields json via {url}", endpoint);
                var json = await client.GetStringAsync(endpoint);
                LogApiRequest(apiMethod);

                var dynObj = JsonSerializer.Deserialize<JsonElement>(json);
                var fnd = dynObj.TryGetProperty("data", out var dataObj);

                if (!fnd || dataObj.ValueKind != JsonValueKind.Array || dataObj.GetArrayLength() == 0)
                {
                    Log.Logger.Error("Failed to find data array element on custom fields call");
                    return new JsonElement();
                }

                return dataObj[0];
            }
        }

        public async Task<bool> AddDetailedDataToEmployee(Employee e, MasterEmployeeRecord mer)
        {
            // sensitive paycom API call
            AddSensitiveDataToEmployee(e, mer);

            // custom fields paycom api call
            if (HasCustomFields)
            {
                var fields = await GetEmployeeCustomFieldsJsonAsync(mer.employee_code);
                AddCustomFieldDataToEmployee(e, fields);
            }

            e.DateOfBirth = mer.birth_date;

            // category detail paycom api call - TODO, make configurable
            return await AddCategoryAttributes(e, "1", mer.cat1);
        }

        private void AddSensitiveDataToEmployee(Employee e, MasterEmployeeRecord mer)
        {
            // we support nicknames when sensitive data enabled
            e.FirstName = FieldService.GetFirstName(mer);
            e.DeptCode = mer.department_code;
            e.DeptName = mer.department_description;
            e.WorkEmail = mer.work_email?.ToLower();
            e.PersonalEmail = mer.personal_email?.ToLower();
            e.PrimaryWorkLocation = FieldService.GetWorkLocationName(mer, GetLocationDirectory());

            FieldService.AddSensitiveDataAttributes(e, mer);

            // currently SSN is only saved when the employee is not connected to any other system (no clockseq). store last 2 digits always.
            // this all may prove to be too customer specific, not sure
            if (!string.IsNullOrEmpty(mer.ss_number))
            {
                var ssnMode = Config.SocialSecurityNumberMode();

                // this is the default, used by Rockhill and anyone else who wants to keep things more secure
                if (ssnMode == SSNMode.LastTwoDigits)
                {
                    if (string.IsNullOrEmpty(e.ClockSeq))
                        e.AddAttribute("ssn", mer.ss_number);

                    e.AddAttribute("ssn_tail", mer.ss_number.Tail(2));
                }
                else
                {
                    // this is for Slim-FourJays, who need the full SSN for aloha
                    e.AddAttribute("ssn", mer.ss_number);
                }
            }

            // hiredate paycom logic, should think about scrapping the rehire override below part perhaps?
            e.HireDate = mer.hire_date;
            if (mer.rehire_date != null && mer.rehire_date > mer.hire_date)
                e.HireDate = (DateTime)mer.rehire_date;

            e.RehireDate = mer.rehire_date;
            e.TermDate = mer.termination_date;
            e.EmployeeAdded = mer.employee_added;

            // add job if there's a position code or position id
            var positionCode = FieldService.GetPrimaryJobCode(mer);
            var positionName = FieldService.GetPrimaryJobName(mer);

            if (!string.IsNullOrEmpty(positionName) || !string.IsNullOrEmpty(positionCode))
            {
                var job = new Job()
                {
                    Code = positionCode,
                    Name = positionName,
                    IsPrimary = true,
                    Rate = string.IsNullOrEmpty(mer.rate_1) ? 0 : Convert.ToDecimal(mer.rate_1),
                    EffectiveDate = mer.last_pay_change
                };

                e.Jobs.Add(job);
            }
        }

        private void AddCustomFieldDataToEmployee(Employee e, JsonElement fields)
        {
            foreach (var customField in Config.CustomFields)
            {
                var fndObj = fields.TryGetProperty(customField.Value, out var valObj);

                if (!fndObj || valObj.ValueKind != JsonValueKind.Object)
                {
                    Log.Logger.Warning("Custom field {0} cannot be found", customField.Value);
                    continue;
                }

                var fndStr = valObj.TryGetProperty("value", out var strObj);

                if (!fndStr || strObj.ValueKind != JsonValueKind.String)
                {
                    Log.Logger.Warning("Custom field {0} value string cannot be found", customField.Value);
                    continue;
                }

                e.AddAttribute(customField.Key, strObj.GetString());
            }
        }

        private async Task<bool> AddCategoryAttributes(Employee e, string catId, string detailCode)
        {
            if (string.IsNullOrEmpty(detailCode)) return false;

            // try to load the allocation if not present
            if (!LaborAllocationDetails.ContainsKey(detailCode))
            {
                var laborAllocation = await GetLaborAllocationDetails(catId, detailCode);
                if (laborAllocation.Count > 0)
                {
                    LaborAllocationDetails.Add(detailCode, laborAllocation.First());
                    Log.Logger.Debug("Found labor allocation for {0}, detail: {1}", catId, detailCode);
                }
            }

            // if we still don't have it by now, return
            if (!LaborAllocationDetails.ContainsKey(detailCode)) return false;

            var la = LaborAllocationDetails[detailCode];
            e.AddAttribute("gl_code", la.glcode);

            return true;
        }

        public async Task<Dictionary<string, Employee>> EmployeeBasicInfoTakeWhile(Predicate<DirectoryEntry> matchFunc, uint fetchMax=0)
        {
            var employees = new Dictionary<string, Employee>();

            uint loaded = await LoadEmployeeDirectory(fetchMax);
            Log.Logger.Debug("Directory Stats: Loaded {0}, FetchMax: {1}", loaded, fetchMax);

            foreach (var e in EmployeeDirectory.Values)
            {
                if (!matchFunc(e)) continue;
                var emp = ConvertDirectoryEntryToEmployee(e);
                employees.Add(emp.Id, emp);
            }

            return employees;
        }

        protected async Task<Dictionary<string, Employee>> EmployeeDetailedTakeWhile(Predicate<DirectoryEntry> matchFunc, uint fetchMax=0)
        {
            var employees = await EmployeeBasicInfoTakeWhile(matchFunc, fetchMax);

            foreach (var e in employees.Keys)
            {
                var masterEmployeeRecord = await GetEmployeeWithMaximalData(e);
                await AddDetailedDataToEmployee(employees[e], masterEmployeeRecord);
            }

            return employees;
        }

        public async Task<IEnumerable<Employee>> AllActiveDetailedEmployee(uint fetchMax = 0)
        {
            var employees = await EmployeeDetailedTakeWhile(emp => Converter.IsActiveEmployee(emp.eestatus), fetchMax);
            return employees.Values; //AttachJobsToEmployees(employees);
        }

        public async Task<IEnumerable<Employee>> AllTerminatedDetailedEmployee(uint fetchMax = 0)
        {
            var employees = await EmployeeDetailedTakeWhile(emp => !Converter.IsActiveEmployee(emp.eestatus), fetchMax);
            return employees.Values;
        }

        private static void LogApiRequest(string endpoint)
        {
            TotalApiRequestsMade++;

            if (ApiCallCounter.ContainsKey(endpoint))
            {
                ApiCallCounter[endpoint]++;
            }
            else
            {
                ApiCallCounter.Add(endpoint, 1);
            }

            if (ApiDelayInSeconds > 0) Thread.Sleep(ApiDelayInSeconds * 1000);
        }

        public static Uri EndpointForPath(string path)
        {
            // hard code endpoint for now
            return new Uri($"https://www.paycomonline.net/v4/rest/index.php/{path}");
        }

        private static HttpClient GetAuthorizedHttpClient()
        {
            var client = new HttpClient();
            client.DefaultRequestHeaders.Authorization = PaycomAuthenticationHeader;
            return client;
        }

        public async Task<List<Location>> GetLocationsAsync()
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/cl/locations";
                var endpoint = EndpointForPath(apiMethod);
                var apiData = await TryFetchApiData(client, endpoint, false); // too noisy
                if (apiData.ValueKind == JsonValueKind.Undefined) return new List<Location>();

                LogApiRequest(apiMethod);
                var locationResponse = JsonSerializer.Deserialize<List<Location>>(apiData);
                return locationResponse;
            }
        }

        public async Task<JsonElement> GetLaborAllocations()
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/cl/category";
                var endpoint = EndpointForPath(apiMethod);
                var apiData = await TryFetchApiData(client, endpoint);
                LogApiRequest(apiMethod);
                return apiData;
            }
        }

        public async Task<List<LaborAllocationDetail>> GetLaborAllocationDetails(string catCode, string laborId)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1.1/cl/category/{0}/detail/{1}";
                var endpoint = EndpointForPath(String.Format(apiMethod, catCode, laborId));

                var apiData = await TryFetchApiData(client, endpoint);
                if (apiData.ValueKind == JsonValueKind.Undefined) return new List<LaborAllocationDetail>();

                var allocResponse = JsonSerializer.Deserialize<List<LaborAllocationDetail>>(apiData);
                LogApiRequest(apiMethod);
                return allocResponse;
            }
        }

        public async Task<JsonElement> GetRawEmployeeWithBasicData(string eecode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));

                var apiData = await TryFetchApiData(client, endpoint);
                LogApiRequest(apiMethod);
                return apiData;
            }
        }

        public async Task<JsonElement> GetRawEmployeeWithSensitiveData(string eecode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}/sensitive";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));

                var apiData = await TryFetchApiData(client, endpoint);
                LogApiRequest(apiMethod);
                return apiData;
            }
        }

        public async Task<JsonElement> GetLaborAllocationsForEmployee(string eecode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}/effectiveratesbyallocation";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));

                var apiData = await TryFetchApiData(client, endpoint);
                LogApiRequest(apiMethod);

                return apiData;
            }
        }

        public async Task<MasterEmployeeRecord> GetEmployeeWithMaximalData(string eecode)
        {
            // get what we are authorized to get...
            JsonElement apiData = new JsonElement();
            if (Config.IncludeSensitive)
                apiData = await GetRawEmployeeWithSensitiveData(eecode);
            else
                apiData = await GetRawEmployeeWithBasicData(eecode);

            if (apiData.ValueKind == JsonValueKind.Undefined) return null;
            var employees = JsonSerializer.Deserialize<List<MasterEmployeeRecord>>(apiData);
            return employees.FirstOrDefault();
        }

        public async Task<IEnumerable<EmployeeIdentifier>> GetActiveEmployeeIds()
        {
            return await GetEmployeeIdsByStatus("A");
        }

        public async Task<IEnumerable<EmployeeIdentifier>> GetInactiveEmployeeIds()
        {
            return await GetEmployeeIdsByStatus("T");
        }

        private async Task<IEnumerable<EmployeeIdentifier>> GetEmployeeIdsByStatus(string status)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = $"api/v1/employeeid/?eestatus={status}";
                var endpoint = EndpointForPath(apiMethod);
                var apiData = await TryFetchApiData(client, endpoint, false); // too noisy
                if (apiData.ValueKind == JsonValueKind.Undefined) return new List<EmployeeIdentifier>();

                LogApiRequest(apiMethod);
                List<EmployeeIdentifier> employeeIdentifiers = JsonSerializer.Deserialize<List<EmployeeIdentifier>>(apiData);
                return employeeIdentifiers;
            }
        }

        public async Task<IEnumerable<DirectoryEntry>> AllEmployeesAsync(uint fetchMax = 0)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                // use large page size, unless asked to load fewer than 500
                var pagesize = fetchMax == 0 ? 500 : Math.Min(500, fetchMax);

                var apiMethod = "api/v1/employeedirectory?pagesize={0}";
                var endpoint = EndpointForPath(String.Format(apiMethod, pagesize));
                Log.Logger.Debug("Fetching first {ps} employees via {url}", pagesize, endpoint);

                var response = await client.GetAsync(endpoint);
                var json = await response.Content.ReadAsStringAsync();
                LogApiRequest(apiMethod);
                Log.Logger.Debug(json);

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<DirectoryEntry>>(json);

                //log the response
                var allEmployees = new List<DirectoryEntry>();
                allEmployees.AddRange(apiResponse.Data);

                //foreach (var hdr in response.Headers) Log.Logger.Debug("{0} = {1}", hdr.Key, hdr.Value);

                // not more than pagesize records?
                if (!response.Headers.Contains("Link") || (fetchMax > 0 && allEmployees.Count() == fetchMax))
                    return allEmployees;

                var links = response.Headers.GetValues("Link");
                var totalCnts = response.Headers.GetValues("X-Total-Count");
                if (links.Count() == 0 || totalCnts.Count() == 0)
                {
                    Log.Logger.Warning("Paycom returned empty 'Link' header or no count total");
                    return allEmployees;
                }

                var regex = new Regex("requestid=([^&]+)");
                var match = regex.Match(links.First());

                if (!match.Success)
                {
                    Log.Logger.Warning("Failed to parse the requestId in {0}", links.First());
                    return allEmployees;
                }

                var requestId = match.Groups[1].Value;
                var totalRemaining = Convert.ToInt32(totalCnts.First()) - pagesize;
                if (fetchMax > 0) totalRemaining = Math.Min(totalRemaining, fetchMax - pagesize);
                var page = 2;

                while (totalRemaining > 0)
                {
                    var myQuery = EndpointForPath($"api/v1/employeedirectory?pagesize={pagesize}&requestid={requestId}&page={page}");
                    Log.Logger.Debug("Fetching {num} employees via {0}", pagesize, myQuery);

                    var nextJson = await client.GetStringAsync(myQuery);
                    LogApiRequest(apiMethod);

                    var nextSet = JsonSerializer.Deserialize<ApiResponse<DirectoryEntry>>(nextJson);
                    allEmployees.AddRange(nextSet.Data);

                    // totalRemaining might go negative here, but not a problem
                    page++;
                    totalRemaining -= pagesize;
                }

                return allEmployees;
            }
        }

        public async Task<IEnumerable<NewHireEntry>> GetNewHireIdsAsync(DateTime startDate, DateTime endDate)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var startTimeStamp = ((DateTimeOffset)startDate).ToUnixTimeSeconds();
                var endTimeStamp = ((DateTimeOffset)endDate).ToUnixTimeSeconds();

                var apiMethod = "api/v1/newhireids?startdate={0}&enddate={1}";
                var endpoint = EndpointForPath(String.Format(apiMethod, startTimeStamp, endTimeStamp));

                Log.Logger.Debug("Fetching new new hires via {url}", endpoint);
                var apiData = await TryFetchApiData(client, endpoint);
                if (apiData.ValueKind == JsonValueKind.Undefined) return new List<NewHireEntry>();

                LogApiRequest(apiMethod);
                return JsonSerializer.Deserialize<List<NewHireEntry>>(apiData);
            }
        }

        public async Task<NewHire> GetNewHireAsync(int nhid)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/newhire/{0}";
                var endpoint = EndpointForPath(String.Format(apiMethod, nhid));
                Log.Logger.Debug("Fetching employee json via {url}", endpoint);

                var apiData = await TryFetchApiData(client, endpoint);
                if (apiData.ValueKind == JsonValueKind.Undefined)
                {
                    Log.Logger.Error("Failed to fetch new hire data for nhid: {0}", nhid);
                    return new NewHire();
                }

                LogApiRequest(apiMethod);
                JsonSerializerOptions options = new JsonSerializerOptions();
                options.Converters.Add(new ZeroDateTimeConverter());

                var newHires = JsonSerializer.Deserialize<IEnumerable<NewHire>>(apiData);
                return newHires.FirstOrDefault();
            }
        }

        public async Task<List<Employee>> GetNewHires(DateTime startDate, DateTime endDate)
        {
            var newHireIds = await GetNewHireIdsAsync(startDate, endDate);
            var newHires = new List<Employee>();

            using (var client = GetAuthorizedHttpClient())
            {
                foreach (var nh in newHireIds)
                {
                    // these new hires dont have enough info to process them yet
                    if (nh.status == "Pending New Hire")
                    {
                        Log.Logger.Information("Skipping pending new hire nhid: {0}, eename: {1}, eecode: {2}", nh.new_hire_id, nh.eename, nh.eecode);
                        continue;
                    }

                    var mer = await GetEmployeeWithMaximalData(nh.eecode);
                    var emp = await ConvertMasterEmployeeRecordToEmployee(mer);

                    newHires.Add(emp);
                }
            }

            return newHires;
        }

        public async Task<IEnumerable<AuditLogEntry>> GetChangesAsync(string eeCode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}/change";
                var endpoint = EndpointForPath(String.Format(apiMethod, eeCode));
                Log.Logger.Debug("Fetching employee changes via {url}", endpoint);

                var apiResponse = await TryFetchApiData(client, endpoint);
                LogApiRequest(apiMethod);

                if (apiResponse.ValueKind == JsonValueKind.Undefined || apiResponse.ValueKind == JsonValueKind.Null)
                    return new List<AuditLogEntry>();

                if (apiResponse.TryGetProperty("records", out var recordElement))
                {
                    var cnt = recordElement.GetInt32();
                    if (cnt == 0) return new List<AuditLogEntry>();
                }

                if (apiResponse.TryGetProperty("data", out var dataElement))
                {
                    if (dataElement.ValueKind == JsonValueKind.Array)
                    {
                        Log.Logger.Error("GetChangesAsync Data Type {0}", dataElement.ValueKind.ToString());
                        return new List<AuditLogEntry>();
                    }

                    return JsonSerializer.Deserialize<IEnumerable<AuditLogEntry>>(dataElement);
                }

                return new List<AuditLogEntry>();
            }
        }

        public async Task<IEnumerable<AuditLogEntry>> GetSensitiveChangesAsync(string eeCode)
        {
            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}/sensitivechange";
                var endpoint = EndpointForPath(String.Format(apiMethod, eeCode));
                Log.Logger.Debug("Fetching sensitive changes via {url}", endpoint);
                var json = await client.GetStringAsync(endpoint);
                LogApiRequest(apiMethod);

                var apiResponse = JsonSerializer.Deserialize<ApiResponse<AuditLogEntry>>(json);
                return apiResponse.Data;
            }
        }

        private async Task<JsonElement> TryFetchApiData(HttpClient client, Uri endpoint, bool logDataInDebugMode = true)
        {
            const int maxRetries = 3;
            const int retryDelaySeconds = 10;

            for (int attempt = 1; attempt <= maxRetries; attempt++)
            {
                try
                {
                    Log.Logger.Debug(messageTemplate: "Fetching {url} (Attempt {attempt}/{maxRetries})", endpoint, attempt, maxRetries);

                    var response = await client.GetAsync(endpoint);

                    if (!response.IsSuccessStatusCode)
                    {
                        if (!response.StatusCode.IsRetryable())
                        {
                            Log.Logger.Debug($"Non-retryable error: {endpoint} => {response.StatusCode}");
                            return new JsonElement();
                        }

                        Log.Logger.Error($"Retriable error: {endpoint} => {response.StatusCode}");

                        if (attempt < maxRetries)
                        {
                            Log.Logger.Warning($"Retrying in {retryDelaySeconds} seconds...");
                            await Task.Delay(retryDelaySeconds * 1000);
                            continue;
                        }

                        return new JsonElement();
                    }

                    var json = await response.Content.ReadAsStringAsync();
                    if (logDataInDebugMode) Log.Logger.Debug(json);

                    var apiResponse = JsonSerializer.Deserialize<JsonElement>(json);
                    if (apiResponse.TryGetProperty("records", out var recordElement))
                    {
                        var cnt = recordElement.GetInt32();
                        if (cnt == 0) return new JsonElement();
                    }

                    var dataElement = apiResponse.GetProperty("data");
                    if (dataElement.ValueKind != JsonValueKind.Array)
                    {
                        Log.Logger.Error("Paycom Response Data Type {0}", dataElement.ValueKind.ToString());
                        return new JsonElement();
                    }

                    return dataElement;
                }
                catch (Exception e)
                {
                    Log.Logger.Error($"Attempt {attempt}/{maxRetries} failed: {e.Message}");

                    if (attempt < maxRetries)
                    {
                        Log.Logger.Warning($"Retrying in {retryDelaySeconds} seconds...");
                        await Task.Delay(retryDelaySeconds * 1000);
                    }
                    else
                    {
                        throw; // Re-throw if all retries failed
                    }
                }
            }

            return new JsonElement();
        }

        public async Task<IEnumerable<AuditLogEntry>> GetAuditLogsAsync(DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var client = GetAuthorizedHttpClient())
                {
                    var startTimeStamp = ((DateTimeOffset)startDate).ToUnixTimeSeconds();
                    var endTimeStamp = ((DateTimeOffset)endDate).ToUnixTimeSeconds();

                    var apiMethod = "api/v1/employeeids/employeechanges?startdate={0}&enddate={1}";
                    var endpoint = EndpointForPath(String.Format(apiMethod, startTimeStamp, endTimeStamp));
                    var apiData = await TryFetchApiData(client, endpoint);
                    LogApiRequest(apiMethod);

                    if (apiData.ValueKind != JsonValueKind.Undefined)
                    {
                        Log.Logger.Debug(JsonSerializer.Serialize(apiData));
                        return JsonSerializer.Deserialize<IEnumerable<AuditLogEntry>>(apiData);
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return new List<AuditLogEntry>();
        }

        public async Task<IEnumerable<Employee>> GetChangedEmployeesAsync(DateTime startDate, DateTime endDate)
        {
            var changedEmployees = new List<Employee>();

            try
            {
                IEnumerable<AuditLogEntry> auditLogs = await GetAuditLogsAsync(startDate, endDate);

                // some audit log entries might not be associated with an eecode
                var eecodes = auditLogs.Where(e => !string.IsNullOrEmpty(e.eecode))
                    .Select(e => e.eecode).Distinct().ToList();

                using (var client = GetAuthorizedHttpClient())
                {
                    foreach (var eecode in eecodes)
                    {
                        MasterEmployeeRecord mer = new MasterEmployeeRecord();

                        // we put this in a try/catch so that we can recover & get the other records
                        try
                        {
                            Log.Logger.Debug(messageTemplate: "Fetching additional data for {0}...", eecode);
                            mer = await GetEmployeeWithMaximalData(eecode);

                            var emp = await ConvertMasterEmployeeRecordToEmployee(mer);
                            emp.LastUpdated = startDate.Date;
                            changedEmployees.Add(emp);
                        }
                        catch (Exception e)
                        {
                            Log.Logger.Fatal("Error Fetching {0}, {1}", eecode, mer.employee_status);
                            Log.Logger.Fatal(e.Message);
                            Log.Logger.Fatal(e.StackTrace);
                        }
                    }
                }
            }
            catch (Exception e)
            {
                Log.Logger.Error(e.Message);
                Log.Logger.Error(e.StackTrace);
            }

            return changedEmployees;
        }

        public async Task<IEnumerable<HistoricalPunch>> GetPunchHistoryAsync(string eecode, DateTime startDate, DateTime endDate)
        {
            try
            {
                using (var client = GetAuthorizedHttpClient())
                {
                    var startTimeStamp = ((DateTimeOffset)startDate).ToUnixTimeSeconds();
                    var endTimeStamp = ((DateTimeOffset)endDate).ToUnixTimeSeconds();

                    var apiMethod = "api/v1/employee/{0}/punchhistory?startdate={1}&enddate={2}";
                    var endpoint = EndpointForPath(String.Format(apiMethod, eecode, startTimeStamp, endTimeStamp));
                    var apiData = await TryFetchApiData(client, endpoint);
                    LogApiRequest(apiMethod);

                    if (apiData.ValueKind == JsonValueKind.Undefined) return new List<HistoricalPunch>();
                    var punchHistoryRecords = JsonSerializer.Deserialize<IEnumerable<HistoricalPunch>>(apiData);
                    Log.Logger.Debug(JsonSerializer.Serialize(punchHistoryRecords));

                    return punchHistoryRecords;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return new List<HistoricalPunch>();
        }

        /// <summary>
        /// Returns historical information about punches for a specified date range.
        /// The Get method returns historical information for an employee's punches in a given date range.
        /// This method defaults to the current pay period of the employee. Date range may not exceed 30 days.
        /// </summary>
        /// <param name="eecode">The four character employee identifier found in the Paycom system.</param>
        /// <param name="startDate">The start date of the request in UNIX format.</param>
        /// <param name="endDate">The end date of the request in UNIX format.</param>
        /// <param name="auditType">Filter the audit by audittype. Valid values: active, changed, deleted, approval. If not provided, all audit types will be returned.</param>
        /// <returns>A PunchAuditApiResponse containing active, changed, deleted, and timecard approval records.</returns>
        public async Task<PunchAuditApiResponse> GetPunchAuditAsync(string eecode, DateTime startDate, DateTime endDate, string auditType = "active")
        {
            try
            {
                using (var client = GetAuthorizedHttpClient())
                {
                    var startTimeStamp = ((DateTimeOffset)startDate).ToUnixTimeSeconds();
                    var endTimeStamp = ((DateTimeOffset)endDate).ToUnixTimeSeconds();

                    var apiMethod = "api/v1/employee/{0}/punchaudit?startdate={1}&enddate={2}&audittype={3}";
                    var endpoint = EndpointForPath(String.Format(apiMethod, eecode, startTimeStamp, endTimeStamp, auditType));
                    
                    var response = await client.GetAsync(endpoint);
                    LogApiRequest(apiMethod);

                    var json = await response.Content.ReadAsStringAsync();
                    Log.Logger.Debug(json);

                    if (!response.IsSuccessStatusCode)
                    {
                        Log.Logger.Error("{0} {1}", response.StatusCode, json);
                        return new PunchAuditApiResponse();
                    }

                    var punchAuditResponse = JsonSerializer.Deserialize<PunchAuditApiResponse>(json);
                    if (!punchAuditResponse.Result)
                    {
                        Log.Logger.Warning("API returned success=false: {0}", string.Join(", ", punchAuditResponse.Errors));
                        return punchAuditResponse;
                    }

                    return punchAuditResponse;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return new PunchAuditApiResponse();
        }

        /// <summary>
        /// Checks if a punch is a duplicate and adds it to the collection if not.
        /// </summary>
        /// <param name="punch">The punch to check and potentially add</param>
        /// <param name="eecode">The employee code for organizing punches</param>
        /// <param name="existingPunchIdsToSkip">Set of existing punch IDs to skip</param>
        /// <param name="paycomPunches">Dictionary to add the punch to if not duplicate</param>
        /// <param name="punchTypeDescription">Description for logging (e.g., "punch", "break-in punch", "break-out punch")</param>
        /// <returns>True if punch was added, false if it was a duplicate</returns>
        private bool TryAddPunchIfNotDuplicate(Punch punch, string eecode, HashSet<string> existingPunchIdsToSkip,
            SortedDictionary<string, List<Punch>> paycomPunches, string punchTypeDescription = "punch")
        {
            string punchId = CachedPunch.GenerateId(punch.EECode, punch.PunchTime, punch.PunchType);
            if (existingPunchIdsToSkip.Contains(punchId))
            {
                Log.Logger.Error("Skipping duplicate {0} from cache: EECode={1}, Time={2}, Type={3}",
                    punchTypeDescription, punch.EECode, punch.PunchTime, punch.PunchType);
                return false;
            }
            else
            {
                if (!paycomPunches.ContainsKey(eecode)) paycomPunches.Add(eecode, new List<Punch>());
                paycomPunches[eecode].Add(punch);
                return true;
            }
        }

        private void AddTipPunches(string eecode, DateTimeOffset businessDate, PunchPair punchPair, SortedDictionary<string, List<Punch>> paycomPunches)
        {
            if (!paycomPunches.ContainsKey(eecode))
            {
                paycomPunches[eecode] = new List<Punch>();
            }

            if (punchPair.CashTip > 0)
            {
                var cashPunch = BuildPaycomEarnPunch(punchPair, businessDate, Convert.ToDouble(punchPair.CashTip), "CST", "Cash tips");
                paycomPunches[eecode].Add(cashPunch);
            }

            if (punchPair.NonCashTip > 0)
            {
                var nonCashPunch = BuildPaycomEarnPunch(punchPair, businessDate, Convert.ToDouble(punchPair.NonCashTip), "TPP", "Paid tips");
                paycomPunches[eecode].Add(nonCashPunch);
            }
        }

        public SortedDictionary<string, List<Punch>> PunchesFromPunchPairs(List<PunchPair> punchPairs, HashSet<string> existingPunchIdsToSkip)
        {
            var total = 0.0;
            var paycomPunches = new SortedDictionary<string, List<Punch>>();
            var breakMode = Config.BreakMode();

            using (var cache = CacheService.OpenCache(SystemConfig.CacheName()))
            {
                var empCache = cache.GetCollection<Employee>(Config.EMPLOYEES_CACHE_COLLECTION);
                foreach (var punchPair in punchPairs)
                {
                    // this eecode variable is used as the primary key to organize the records in the dictionary we return
                    string eecode;

                    if (FieldService.UseClockSeqAsEmployeeId())
                    {
                        eecode = punchPair.ClockSeq;
                        var emp = empCache.FindOne(x => x.ClockSeq == eecode);
                        if (emp == null)
                        {
                            Log.Logger.Warning("Invalid clockseq {0} in punch with timeIn {1}, should be skipped...", eecode, punchPair.TimeIn);
                            continue;
                        }
                    }
                    else
                    {
                        eecode = punchPair.EECode;
                        var emp = empCache.FindById(eecode);
                        if (emp == null)
                        {
                            Log.Logger.Warning("Invalid eecode {0} in punch with timeIn {1}, should be skipped...", eecode, punchPair.TimeIn);
                            continue;
                        }
                    }

                    var timeAdjust = new TimeSpan(Config.TimeZoneAdjust(punchPair.TimeZone), 0, 0);

                    if (punchPair.TimeIn == DateTime.MinValue)
                    {
                        var businessDate = punchPair.Date + timeAdjust;
                        Log.Logger.Warning($"Tip only punch. Employee: {eecode}, Date: {businessDate}");
                        AddTipPunches(eecode, businessDate, punchPair, paycomPunches);
                        continue;
                    }

                    // ID means In Day; used to begin a shift.
                    var timeIn = punchPair.TimeIn + timeAdjust;
                    Punch punchIn = BuildPaycomPunch(punchPair, timeIn, "ID");

                    // Check for duplicate before adding to the list
                    TryAddPunchIfNotDuplicate(punchIn, eecode, existingPunchIdsToSkip, paycomPunches);

                    // OD means Out Day; used to end a shift.
                    if (punchPair.TimeOut == DateTime.MinValue)
                    {
                        // employee failed to punch out...
                        Log.Logger.Warning($"Skipping time out punch, invalid time. Employee: {eecode}, Date: {punchPair.Date}");
                        continue;
                    }

                    var timeOut = punchPair.TimeOut + timeAdjust;
                    var punchOut = BuildPaycomPunch(punchPair, timeOut, "OD");

                    // Check for duplicate before adding to the list
                    TryAddPunchIfNotDuplicate(punchOut, eecode, existingPunchIdsToSkip, paycomPunches);

                    AddTipPunches(eecode, timeOut, punchPair, paycomPunches);

                    foreach (var ppBreak in punchPair.Breaks)
                    {
                        if (ppBreak.TimeIn != DateTime.MinValue)
                        {
                            var breakInTime = ppBreak.TimeIn + timeAdjust;
                            var breakIn = BuildPaycomPunch(punchPair, breakInTime, Config.StartBreakCode(breakMode, ppBreak));

                            // Check for duplicate before adding to the list
                            TryAddPunchIfNotDuplicate(breakIn, eecode, existingPunchIdsToSkip, paycomPunches, "break-in punch");
                        }

                        if (ppBreak.TimeOut != DateTime.MinValue)
                        {
                            var breakOutTime = ppBreak.TimeOut + timeAdjust;
                            var breakOut = BuildPaycomPunch(punchPair, breakOutTime, Config.EndBreakCode(breakMode, ppBreak));

                            // Check for duplicate before adding to the list
                            TryAddPunchIfNotDuplicate(breakOut, eecode, existingPunchIdsToSkip, paycomPunches, "break-out punch");
                        }
                    }

                    var hours = punchPair.TimeOut - punchPair.TimeIn;
                    total += hours.TotalHours;
                }
            }

            Log.Logger.Information($"PunchesFromPunchPairs: total hours={total}");
            return paycomPunches;
        }

        private string GetPaycomTimeZone(PunchPair punchPair)
        {
            if (string.IsNullOrEmpty(punchPair.TimeZone))
                return ConfigTimeZone;

            // paycom expects the timezone to be EST, CST, MST, PST
            if (punchPair.TimeZone.Length > 3)
                return TimeService.TimeZoneFromIanaName(punchPair.TimeZone);

            return punchPair.TimeZone;
        }

        public Punch BuildPaycomPunch(PunchPair punchPair, DateTimeOffset dto, string punchType)
        {
            // build paycom time string
            var punchTime = dto.ToUnixTimeSeconds().ToString();

            // use punch provided timezone or else the PAYCOM_TIMEZONE
            var timeZone = GetPaycomTimeZone(punchPair);

            var punch = new Punch()
            {
                ExternalId = punchPair.Id,
                ClockSeq = punchPair.ClockSeq,
                Description = string.Format("{0:yyyy-MM-dd HH:mm:ss zzz}", dto.ToLocalTime()),
                PunchTime = punchTime,
                TimeZone = timeZone,
                EntryType = 1, // Punch Entry
                PunchType = punchType,
            };

            FieldService.SetPunchLocation(punch, punchPair.Location);

            return punch;
        }

        public Punch BuildPaycomEarnPunch(PunchPair punchPair, DateTimeOffset dto, double dollaramount, string earnCode, string description)
        {
            // build paycom time string
            var punchTime = dto.ToUnixTimeSeconds().ToString();

            // use punch provided timezone or else the PAYCOM_TIMEZONE
            var timeZone = GetPaycomTimeZone(punchPair);

            var punch = new Punch()
            {
                ExternalId = punchPair.Id,
                ClockSeq = punchPair.ClockSeq,
                Description = string.Format("{0:yyyy-MM-dd HH:mm:ss zzz}", dto),
                EarnCode = earnCode,
                PunchTime = punchTime,
                TimeZone = timeZone,
                EntryType = 3, // Earning report Entry
                DollarAmount = dollaramount
            };

            FieldService.SetPunchLocation(punch, punchPair.Location);
            return punch;
        }

        public async Task<int> ImportPunchesAsync(ExecutionMode executionMode, SortedDictionary<string, List<Punch>> punchesById)
        {
            int totalPunches = 0;
            const int batchSize = 2000;

            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1.1/punchimport";
                var endpoint = EndpointForPath(apiMethod);

                var keys = new Stack<string>(punchesById.Keys.OrderBy(k => k));

                while (keys.Count > 0)
                {
                    var batch = new List<Punch>();
                    int employeeCount = 0;

                    while (keys.Count > 0 && batch.Count < batchSize)
                    {
                        var key = keys.Pop();
                        var punches = punchesById[key];

                        if (batch.Count + punches.Count > batchSize)
                        {
                            keys.Push(key); // Put the key back if it would exceed the batch size
                            break;
                        }

                        batch.AddRange(punches);
                        employeeCount++;
                    }

                    if (batch.Count == 0) break;

                    Log.Logger.Information("{label} {cnt} punches for {empCnt} employees",
                        executionMode == ExecutionMode.Execute ? "Sending" : "Would have sent", batch.Count, employeeCount);

                    var body = JsonSerializer.Serialize(batch, Json.DefaultSerializerOutputStyle);
                    var content = new StringContent(body, Encoding.UTF8, new MediaTypeHeaderValue("application/json"));

                    if (executionMode == ExecutionMode.Execute)
                    {
                        var response = await client.PostAsync(endpoint, content);
                        LogApiRequest(apiMethod);
                        string responseBody = await response.Content.ReadAsStringAsync();

                        if (response.IsSuccessStatusCode)
                        {
                            totalPunches += batch.Count;
                            continue;
                        }

                        Log.Logger.Error("  Failed to import punches: {msg}", response.ReasonPhrase);
                        Log.Logger.Error("API Response: {response}", responseBody);
                        var apiResponse = JsonSerializer.Deserialize<ApiPunchResponse>(responseBody);
                        foreach (var error in apiResponse.Errors)
                        {
                            var errorMessage = string.Join(", ", error.Errors);
                            if (errorMessage == "No Employee Found. eecode, eebadge, or clocksequencenumber is required. If provided, make sure values are correct for the intended employee.")
                            {
                                Log.Logger.Error("Invalid clockseq detected: {0}", error.Punch.ClockSeq);
                                foreach (var punch in batch)
                                {
                                    if (punch.ClockSeq == error.Punch.ClockSeq)
                                    {
                                       batch.Remove(punch);
                                    }
                                }
                            }
                            else
                            {
                                Log.Logger.Error("Punch Error: {0}", errorMessage);
                                Log.Logger.Error(JsonSerializer.Serialize(error.Punch));
                            }
                        }

                        Log.Logger.Debug(responseBody);
                    }
                    else
                    {
                        Log.Logger.Debug(body);
                        totalPunches += batch.Count;
                    }
                }

                return totalPunches;
            }
        }

        public async Task<bool> UpdatePunchAsync(string punchId, PunchPair punchPair)
        {
            HttpContent content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("fieldname", "clockse"),
                new KeyValuePair<string, string>("value", "newvalue")
            });

            try
            {
                using (var client = GetAuthorizedHttpClient())
                {
                    var apiMethod = "api/v1/punchimport/{0}";
                    var endpoint = EndpointForPath(String.Format(apiMethod, punchId));
                    var response = await client.PatchAsync(endpoint, content);
                    LogApiRequest(apiMethod);

                    if (!response.IsSuccessStatusCode)
                    {
                        Log.Logger.Error("{0} {1}", response.StatusCode, await response.Content.ReadAsStringAsync());
                        return false;
                    }

                    return true;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return false;
        }

        public async Task<bool> DeletePunchAsync(string punchId)
        {
            try
            {
                using (var client = GetAuthorizedHttpClient())
                {
                    var apiMethod = "api/v1/punchimport/{0}";
                    var endpoint = EndpointForPath(String.Format(apiMethod, punchId));
                    var response = await client.DeleteAsync(endpoint);
                    //LogApiRequest(apiMethod);

                    if (!response.IsSuccessStatusCode)
                    {
                        Log.Logger.Error("{0} {1}", response.StatusCode, await response.Content.ReadAsStringAsync());
                        return false;
                    }

                    return true;
                }
            }
            catch (Exception e)
            {
                Log.Logger.Fatal(e.Message);
                Log.Logger.Fatal(e.StackTrace);
            }

            return false;
        }

        public async Task<bool> PatchEmployeeAsync(string eecode, string fieldname, string newvalue)
        {
            HttpContent content = new FormUrlEncodedContent(new[]
            {
                new KeyValuePair<string, string>("fieldname", fieldname),
                new KeyValuePair<string, string>("value", newvalue)
            });

            using (var client = GetAuthorizedHttpClient())
            {
                var apiMethod = "api/v1/employee/{0}";
                var endpoint = EndpointForPath(String.Format(apiMethod, eecode));
                Log.Logger.Debug("Patching employee via {url}, {fld}={val}", endpoint, fieldname, newvalue);
                var response = await client.PatchAsync(endpoint, content);
                LogApiRequest(apiMethod);

                if (!response.IsSuccessStatusCode)
                {
                    Log.Logger.Error("{0} {1}", response.StatusCode, await response.Content.ReadAsStringAsync());
                    return false;
                }

                return true;
            }
        }

        public async Task<bool> UpdateClockSeqAsync(string eecode, string clockSeq)
        {
            return await PatchEmployeeAsync(eecode, "clockseq", clockSeq);
        }

        public async Task<bool> UpdateEmailAddressAsync(string eecode, string emailAddr)
        {
            return await PatchEmployeeAsync(eecode, "email", emailAddr);
        }

        public async Task<bool> UpdateBadgeNoAsync(string eecode, string badgeNo)
        {
            return await PatchEmployeeAsync(eecode, "eebadge", badgeNo);
        }

        void IDisposable.Dispose()
        {
            var endTime = DateTime.Now;
            var totalTime = endTime - StartTime;
            var requestsPerSec = TotalApiRequestsMade / totalTime.TotalSeconds;

            // update cache stats
            CacheService.CacheStats(ApiCallCounter.ToList());

            Log.Logger.Information("Total Api Requests Made = {total}", TotalApiRequestsMade);
            Log.Logger.Information("Elapsed Time in Seconds = {time}", totalTime.TotalSeconds);

            if (requestsPerSec > 15)
                Log.Logger.Warning("Requests Per Second     = {stat}", requestsPerSec);
            else
                Log.Logger.Debug("Requests Per Second     = {stat}", requestsPerSec);
        }
    }
}
