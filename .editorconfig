# EditorConfig is awesome: https://EditorConfig.org

# top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
charset = utf-8

# 4 space indentation for most files
[*]
indent_style = space
indent_size = 4

# 2 space indentation for JSON, YAML, and web files
[*.{json,yml,yaml,html,css,scss,js,ts,jsx,tsx}]
indent_size = 2

# Tab indentation for Makefiles
[Makefile]
indent_style = tab

# Long lines for markdown
[*.md]
max_line_length = off
trim_trailing_whitespace = false
