# Git Authentication in Devcontainer - Best Practices

## Problem
The error `**************: Permission denied (publickey)` occurs because the devcontainer doesn't have access to your SSH keys or proper Git authentication.

## Recommended Solutions

### Option 1: SSH Key Forwarding (Most Secure)

This approach forwards your host SSH keys to the container.

#### Prerequisites
1. Ensure you have SSH keys set up on your host machine:
   ```bash
   # Check if you have SSH keys
   ls -la ~/.ssh/
   
   # If no keys exist, generate them
   ssh-keygen -t ed25519 -C "<EMAIL>"
   
   # Add to GitHub
   cat ~/.ssh/id_ed25519.pub
   ```

2. Add your public key to GitHub (Settings → SSH and GPG keys)

#### Setup
The devcontainer is already configured with SSH key mounting. After rebuilding:

```bash
# Test SSH connection
ssh -T **************

# If successful, you should see:
# Hi username! You've successfully authenticated...
```

### Option 2: HTTPS with Personal Access Token (Fallback)

If SSH doesn't work, use HTTPS authentication with your GitHub token.

#### Setup
1. Your `GITHUB_TOKEN` and `GITHUB_USERNAME` are already configured in the devcontainer
2. The setup script automatically configures Git to use HTTPS with token authentication
3. This converts SSH URLs to HTTPS automatically

#### Manual Configuration (if needed)
```bash
# Configure Git to use HTTPS instead of SSH
git config --global url."https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com/".insteadOf "**************:"

# Or change the remote URL for this repository
git remote set-url origin https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com/your-org/payroll-tools.git
```

### Option 3: GitHub CLI Authentication

```bash
# Install GitHub CLI (if not already installed)
curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | sudo dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg
echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | sudo tee /etc/apt/sources.list.d/github-cli.list > /dev/null
sudo apt update
sudo apt install gh

# Authenticate
gh auth login --with-token <<< "$GITHUB_TOKEN"

# Configure Git to use GitHub CLI
gh auth setup-git
```

## Troubleshooting

### 1. SSH Key Issues
```bash
# Check SSH agent
ssh-add -l

# Add keys manually if needed
ssh-add ~/.ssh/id_rsa
ssh-add ~/.ssh/id_ed25519

# Test connection with verbose output
ssh -vT **************
```

### 2. Permission Issues
```bash
# Fix SSH key permissions
chmod 700 ~/.ssh
chmod 600 ~/.ssh/id_*
chmod 644 ~/.ssh/*.pub
```

### 3. Token Issues
```bash
# Verify token is set
echo $GITHUB_TOKEN | cut -c1-10  # Should show first 10 chars

# Test token validity
curl -H "Authorization: token $GITHUB_TOKEN" https://api.github.com/user
```

### 4. Git Configuration Check
```bash
# Check current Git configuration
git config --list | grep -E "(url|credential)"

# Check remote URLs
git remote -v
```

## Quick Fix Commands

### Immediate Solution (HTTPS)
```bash
# Convert current repository to use HTTPS
git remote set-url origin https://${GITHUB_USERNAME}:${GITHUB_TOKEN}@github.com/$(git remote get-url origin | sed 's/.*github.com[:/]\(.*\)\.git/\1/').git

# Test
git pull --tags origin main
```

### Reset Git Configuration
```bash
# Remove SSH-to-HTTPS conversion
git config --global --unset url."https://github.com/".insteadOf

# Reset to SSH
git remote set-<NAME_EMAIL>:$(git remote get-url origin | sed 's/.*github.com\/\(.*\)\.git/\1/').git
```

## Security Best Practices

1. **Use SSH keys when possible** - More secure than tokens
2. **Limit token permissions** - Only grant necessary scopes
3. **Rotate tokens regularly** - Generate new tokens periodically
4. **Never commit credentials** - Always use environment variables
5. **Use different tokens per environment** - Separate tokens for dev/prod

## Environment Variables Required

Ensure these are set in your host environment:
```bash
export GITHUB_USERNAME="your-github-username"
export GITHUB_TOKEN="ghp_your_token_here"
```

The devcontainer will automatically use these for authentication.
