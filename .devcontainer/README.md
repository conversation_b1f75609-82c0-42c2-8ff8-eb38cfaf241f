# GitHub Container Registry Authentication for Devcontainer

This devcontainer is configured to automatically authenticate with GitHub Container Registry (ghcr.io) when the appropriate environment variables are provided.

## Setup Instructions

### 1. Create a GitHub Personal Access Token

1. Go to GitHub Settings → Developer settings → Personal access tokens → Tokens (classic)
2. Click "Generate new token (classic)"
3. Give it a descriptive name like "Payroll Tools Container Registry"
4. Select the following scopes:
   - `write:packages` (to push containers)
   - `read:packages` (to pull containers)
   - `delete:packages` (optional, to delete containers)
5. Click "Generate token" and copy the token value

### 2. Set Environment Variables on Your Host System

#### For macOS/Linux (add to your shell profile):

```bash
export GITHUB_USERNAME="your-github-username"
export GITHUB_TOKEN="ghp_your_token_here"
```

#### For Windows (PowerShell):

```powershell
$env:GITHUB_USERNAME="your-github-username"
$env:GITHUB_TOKEN="ghp_your_token_here"
```

Or set them permanently in System Properties → Environment Variables.

### 3. Rebuild the Devcontainer

After setting the environment variables:

1. In VS Code, open the Command Palette (Ctrl/Cmd + Shift + P)
2. Run "Dev Containers: Rebuild Container"
3. The setup script will automatically authenticate with ghcr.io during container creation

## Verification

To verify authentication is working:

```bash
podman login --get-login ghcr.io
```

This should return your GitHub username if authentication was successful.

## Manual Authentication (if needed)

If you need to authenticate manually within the container:

```bash
echo $GITHUB_TOKEN | podman login ghcr.io -u $GITHUB_USERNAME --password-stdin
```

## Security Notes

- Never commit tokens to version control
- Use environment variables to keep credentials secure
- Each developer should use their own GitHub token
- Tokens can be revoked and regenerated as needed

## Troubleshooting

### "Not authenticated with ghcr.io" error

1. Verify environment variables are set: `echo $GITHUB_USERNAME $GITHUB_TOKEN`
2. Check token permissions include `write:packages`
3. Rebuild the devcontainer after setting variables
4. Try manual authentication command above

### Token expired or invalid

1. Generate a new token following step 1 above
2. Update the `GITHUB_TOKEN` environment variable
3. Rebuild the devcontainer
